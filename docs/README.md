# 双Token认证机制文档

本文件夹包含双Token认证机制的完整实现文档和测试文件。

## 文件说明

### 📋 文档文件

1. **[双token实现说明.md](./双token实现说明.md)**
   - 双Token认证机制的完整实现说明
   - 包含修改的文件列表和关键代码
   - 工作流程和特性介绍
   - 使用方式说明

2. **[修复说明.md](./修复说明.md)**
   - 修复HTTP请求头错误的详细说明
   - 问题原因分析和解决方案
   - 方法名冲突问题的修复过程

### 🧪 测试文件

3. **[test_dual_token.html](./test_dual_token.html)**
   - 双Token功能的交互式测试页面
   - 包含Token存储、登录响应、刷新等测试
   - 可以在浏览器中直接打开使用

## 快速开始

### 查看实现说明
```bash
# 查看主要实现文档
cat docs/双token实现说明.md

# 查看问题修复说明
cat docs/修复说明.md
```

### 运行测试
```bash
# 在浏览器中打开测试页面
open docs/test_dual_token.html
```

## 实现概述

双Token认证机制已成功实现，主要特性：

✅ **自动token刷新** - 在accessToken过期时自动使用refreshToken刷新  
✅ **并发请求队列管理** - 避免多个请求同时刷新token  
✅ **向后兼容** - 支持原有的单token格式  
✅ **完整错误处理** - 刷新失败时自动跳转登录页面  
✅ **匹配后端实现** - 使用refresh-token请求头  

## 修改的文件

- `src/utils/auth.js` - 添加refreshToken的Cookie存储功能
- `src/store/modules/user.js` - 修改登录和刷新逻辑
- `src/utils/request.js` - 实现自动token刷新机制
- `src/api/login.js` - 添加refresh接口

## 使用方式

现有代码无需修改，双token功能在底层自动工作：

1. **登录时**: 自动保存accessToken和refreshToken
2. **API请求**: 自动使用accessToken
3. **Token过期**: 自动刷新并重试请求
4. **刷新失败**: 自动跳转登录页面

## 后端接口要求

- **刷新接口**: `POST /refresh`
- **请求头**: `refresh-token: {refreshToken}`
- **响应格式**: 
```json
{
  "msg": "刷新成功",
  "code": 200,
  "data": {
    "accessToken": "新的accessToken",
    "refreshToken": "新的refreshToken"
  }
}
```
