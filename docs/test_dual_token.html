<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双Token功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>双Token认证机制测试页面</h1>
    
    <div class="test-section">
        <h3>1. Token存储测试</h3>
        <button onclick="testTokenStorage()">测试Token存储</button>
        <div id="tokenStorageResult"></div>
    </div>

    <div class="test-section">
        <h3>2. 模拟登录响应处理</h3>
        <button onclick="testLoginResponse()">测试登录响应处理</button>
        <div id="loginResponseResult"></div>
    </div>

    <div class="test-section">
        <h3>3. 模拟Token刷新</h3>
        <button onclick="testTokenRefresh()">测试Token刷新</button>
        <div id="tokenRefreshResult"></div>
    </div>

    <div class="test-section">
        <h3>4. 当前Token状态</h3>
        <button onclick="checkTokenStatus()">检查Token状态</button>
        <div id="tokenStatusResult"></div>
    </div>

    <script>
        // 模拟Cookie操作（简化版）
        const mockCookies = {};
        
        const mockAuth = {
            getToken: () => mockCookies['Admin-Token'],
            setToken: (token) => mockCookies['Admin-Token'] = token,
            removeToken: () => delete mockCookies['Admin-Token'],
            getRefreshToken: () => mockCookies['Admin-Refresh-Token'],
            setRefreshToken: (token) => mockCookies['Admin-Refresh-Token'] = token,
            removeRefreshToken: () => delete mockCookies['Admin-Refresh-Token']
        };

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function testTokenStorage() {
            try {
                // 测试accessToken存储
                mockAuth.setToken('test-access-token');
                const accessToken = mockAuth.getToken();
                
                // 测试refreshToken存储
                mockAuth.setRefreshToken('test-refresh-token');
                const refreshToken = mockAuth.getRefreshToken();
                
                if (accessToken === 'test-access-token' && refreshToken === 'test-refresh-token') {
                    showResult('tokenStorageResult', 
                        `✅ Token存储测试通过<br>
                        AccessToken: ${accessToken}<br>
                        RefreshToken: ${refreshToken}`, 'success');
                } else {
                    showResult('tokenStorageResult', '❌ Token存储测试失败', 'error');
                }
            } catch (error) {
                showResult('tokenStorageResult', `❌ 测试出错: ${error.message}`, 'error');
            }
        }

        function testLoginResponse() {
            try {
                // 模拟登录响应
                const mockLoginResponse = {
                    msg: "操作成功",
                    code: 200,
                    data: {
                        accessToken: "eyJhbGciOiJIUzUxMiJ9.mock-access-token",
                        refreshToken: "eyJhbGciOiJIUzUxMiJ9.mock-refresh-token"
                    }
                };

                // 模拟处理登录响应的逻辑
                const accessToken = mockLoginResponse.data?.accessToken || mockLoginResponse.token;
                const refreshToken = mockLoginResponse.data?.refreshToken;

                mockAuth.setToken(accessToken);
                if (refreshToken) {
                    mockAuth.setRefreshToken(refreshToken);
                }

                showResult('loginResponseResult', 
                    `✅ 登录响应处理测试通过<br>
                    <pre>${JSON.stringify(mockLoginResponse, null, 2)}</pre>
                    存储的AccessToken: ${mockAuth.getToken()}<br>
                    存储的RefreshToken: ${mockAuth.getRefreshToken()}`, 'success');
            } catch (error) {
                showResult('loginResponseResult', `❌ 测试出错: ${error.message}`, 'error');
            }
        }

        function testTokenRefresh() {
            try {
                // 设置初始token
                mockAuth.setToken('old-access-token');
                mockAuth.setRefreshToken('valid-refresh-token');

                // 模拟刷新响应
                const mockRefreshResponse = {
                    msg: "刷新成功",
                    code: 200,
                    data: {
                        accessToken: "new-access-token-" + Date.now(),
                        refreshToken: "new-refresh-token-" + Date.now()
                    }
                };

                // 模拟刷新token的处理逻辑
                const newAccessToken = mockRefreshResponse.data?.accessToken || mockRefreshResponse.accessToken;
                const newRefreshToken = mockRefreshResponse.data?.refreshToken || mockRefreshResponse.refreshToken;

                if (newAccessToken) {
                    mockAuth.setToken(newAccessToken);
                }
                if (newRefreshToken) {
                    mockAuth.setRefreshToken(newRefreshToken);
                }

                showResult('tokenRefreshResult', 
                    `✅ Token刷新测试通过<br>
                    <pre>${JSON.stringify(mockRefreshResponse, null, 2)}</pre>
                    新的AccessToken: ${mockAuth.getToken()}<br>
                    新的RefreshToken: ${mockAuth.getRefreshToken()}`, 'success');
            } catch (error) {
                showResult('tokenRefreshResult', `❌ 测试出错: ${error.message}`, 'error');
            }
        }

        function checkTokenStatus() {
            const accessToken = mockAuth.getToken();
            const refreshToken = mockAuth.getRefreshToken();
            
            let status = '';
            if (accessToken && refreshToken) {
                status = '✅ 双Token状态正常';
            } else if (accessToken) {
                status = '⚠️ 只有AccessToken，缺少RefreshToken';
            } else if (refreshToken) {
                status = '⚠️ 只有RefreshToken，缺少AccessToken';
            } else {
                status = '❌ 没有任何Token';
            }

            showResult('tokenStatusResult', 
                `${status}<br>
                AccessToken: ${accessToken || '无'}<br>
                RefreshToken: ${refreshToken || '无'}<br>
                <br>
                <strong>Cookie存储状态:</strong><br>
                <pre>${JSON.stringify(mockCookies, null, 2)}</pre>`, 
                accessToken && refreshToken ? 'success' : 'info');
        }

        // 页面加载时显示说明
        window.onload = function() {
            showResult('tokenStatusResult', 
                '点击"检查Token状态"按钮查看当前Token状态', 'info');
        };
    </script>
</body>
</html>
