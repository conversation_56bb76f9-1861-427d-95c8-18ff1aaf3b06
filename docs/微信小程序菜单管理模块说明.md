# 微信小程序菜单管理模块

## 概述

本模块实现了忻道中医生命急救三分钟管理平台的微信小程序菜单管理功能，支持基于用户角色的动态菜单权限控制。

## 功能特性

### ✅ 已实现功能

1. **菜单管理**
   - 菜单的增删改查
   - 菜单状态控制（启用/禁用）
   - 菜单排序管理
   - 支持个人设置和功能菜单两种类型

2. **权限控制**
   - 基于用户角色的菜单过滤
   - 支持多角色权限验证
   - 角色申请权限检查

3. **前端组件**
   - 菜单列表显示组件
   - 权限验证组件
   - 权限工具类

4. **管理后台**
   - 菜单配置管理页面
   - 菜单演示页面

## 文件结构

```
src/
├── api/
│   ├── bizz/
│   │   └── wx_menu.js              # 后台菜单管理API
│   └── wx/
│       └── menu.js                 # 前端菜单获取API
├── components/
│   ├── WxMenuAuth/
│   │   └── index.vue               # 权限验证组件
│   └── WxMenuList/
│       └── index.vue               # 菜单列表显示组件
├── utils/
│   └── wxMenuAuth.js               # 权限工具类
└── views/
    └── bizz/
        └── wx_menu/
            ├── index.vue           # 菜单管理页面
            └── demo.vue            # 菜单演示页面
```

## 角色系统

### 支持的角色类型

- `general`: 普通用户
- `aider`: 急救员  
- `mentor`: 急救导师
- `disciple`: 弟子

### 角色升级规则

- 普通用户 → 急救员
- 普通用户/急救员 → 急救导师
- 急救导师 → 收弟子

## API接口

### 前端菜单接口

```javascript
// 获取个人设置菜单
GET /wx/menu/personal

// 获取功能菜单
GET /wx/menu/function

// 检查菜单权限
GET /api/menu/check/{menuId}

// 获取用户角色
GET /api/user/roles
```

### 管理后台接口

```javascript
// 菜单CRUD操作
GET    /bizz/wx_menu/list      # 查询菜单列表
GET    /bizz/wx_menu/{id}      # 查询菜单详情
POST   /bizz/wx_menu           # 新增菜单
PUT    /bizz/wx_menu           # 修改菜单
DELETE /bizz/wx_menu/{id}      # 删除菜单

// 菜单管理操作
PUT /bizz/wx_menu/{id}/status  # 更新菜单状态
PUT /bizz/wx_menu/{id}/sort    # 更新菜单排序
GET /bizz/wx_menu/manage       # 获取管理后台菜单
```

## 组件使用

### 1. 菜单列表组件

```vue
<template>
  <!-- 个人设置菜单 -->
  <WxMenuList 
    menu-type="personal" 
    :columns="4"
    @menu-click="handleMenuClick"
  />
  
  <!-- 功能菜单 -->
  <WxMenuList 
    menu-type="function" 
    :columns="3"
    @menu-click="handleMenuClick"
  />
</template>

<script setup>
import WxMenuList from '@/components/WxMenuList/index.vue';

function handleMenuClick(menu) {
  console.log('点击菜单:', menu);
}
</script>
```

### 2. 权限验证组件

```vue
<template>
  <!-- 角色权限验证 -->
  <WxMenuAuth :roles="['aider', 'mentor']">
    <el-button>急救员或导师可见</el-button>
  </WxMenuAuth>
  
  <!-- 菜单权限验证 -->
  <WxMenuAuth :menu-id="123">
    <el-button>有菜单权限才可见</el-button>
  </WxMenuAuth>
</template>

<script setup>
import WxMenuAuth from '@/components/WxMenuAuth/index.vue';
</script>
```

### 3. 权限工具类

```javascript
import { useWxMenuAuth } from '@/utils/wxMenuAuth';

const {
  getUserRoles,
  hasRole,
  hasMenuPermission,
  filterMenusByRole,
  canApplyForRole
} = useWxMenuAuth();

// 检查用户角色
const userRoles = await getUserRoles();

// 检查是否有指定角色
const isAider = await hasRole('aider');

// 检查菜单权限
const hasPermission = await hasMenuPermission(123);

// 过滤菜单
const filteredMenus = await filterMenusByRole(menus);

// 检查是否可以申请角色
const canApply = await canApplyForRole('mentor');
```

## 菜单配置示例

### 个人设置菜单

```json
[
  {
    "id": 1,
    "title": "账户设置",
    "icon": "setting-fill",
    "path": "/pageA/profile",
    "showFor": ["all"],
    "description": "用户账户基本设置"
  },
  {
    "id": 2,
    "title": "个人资料",
    "icon": "account-fill", 
    "path": "/pageA/personal",
    "showFor": ["aider", "mentor", "disciple"],
    "description": "完善个人详细信息"
  }
]
```

### 功能菜单

```json
[
  {
    "id": 4,
    "title": "证件申请",
    "icon": "order-fill",
    "path": "/pageA/apply/list",
    "showFor": ["aider", "mentor", "disciple"],
    "description": "申请相关资质证件"
  },
  {
    "id": 1001,
    "title": "急救员申请",
    "icon": "plus-circle-fill",
    "path": "/pageA/user_apply/index?userType=aider",
    "showFor": ["general"],
    "description": "普通用户申请成为急救员"
  }
]
```

## 数据库设计

### 菜单表 (wx_menus)

```sql
CREATE TABLE wx_menus (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(50) NOT NULL COMMENT '菜单名称',
  icon VARCHAR(50) NOT NULL COMMENT '图标名称',
  path VARCHAR(200) NOT NULL COMMENT '页面路径',
  menu_type VARCHAR(200) NOT NULL COMMENT '菜单类型',
  show_for VARCHAR(200) COMMENT '显示给哪些角色',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  description VARCHAR(200) COMMENT '菜单描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 使用说明

### 1. 管理员配置菜单

1. 访问 `/bizz/wx_menu` 页面
2. 添加或编辑菜单项
3. 设置菜单类型、显示角色、排序等
4. 启用菜单

### 2. 前端显示菜单

1. 在页面中使用 `WxMenuList` 组件
2. 指定菜单类型（personal/function）
3. 组件会自动根据用户角色过滤菜单

### 3. 权限验证

1. 使用 `WxMenuAuth` 组件包装需要权限控制的内容
2. 或使用 `useWxMenuAuth` 工具类进行编程式权限检查

## 注意事项

1. 菜单权限基于用户角色，需要确保后端正确返回用户角色信息
2. 菜单配置支持热更新，修改后立即生效
3. 权限验证组件会缓存用户角色信息，提高性能
4. 建议在生产环境中使用Redis缓存菜单配置
