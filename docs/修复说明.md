# 双Token实现 - 错误修复

## 问题描述

遇到错误：
```
SyntaxError: Failed to execute 'setRequestHeader' on 'XMLHttpRequest': 'function() {...}' is not a valid HTTP header field value.
```

## 问题原因

在 `src/store/modules/user.js` 中，方法名 `refreshToken()` 与属性名 `this.refreshToken` 冲突。

当调用 `this.refreshToken` 时，返回的是方法本身而不是refreshToken的值，导致HTTP请求头设置了一个函数对象。

## 解决方案

### 1. 重命名方法 (src/store/modules/user.js)

**修改前：**
```javascript
refreshToken() {
  return new Promise((resolve, reject) => {
    const currentRefreshToken = this.refreshToken  // 这里会返回方法本身！
    // ...
  })
}
```

**修改后：**
```javascript
refreshAccessToken() {
  return new Promise((resolve, reject) => {
    const currentRefreshToken = this.refreshToken  // 现在正确返回token值
    // ...
  })
}
```

### 2. 更新调用处 (src/utils/request.js)

**修改前：**
```javascript
return useUserStore().refreshToken().then((newToken) => {
```

**修改后：**
```javascript
return useUserStore().refreshAccessToken().then((newToken) => {
```

## 修复结果

✅ 解决了方法名与属性名的冲突  
✅ HTTP请求头现在正确设置refreshToken字符串值  
✅ 双token刷新机制正常工作  

## 使用方式

```javascript
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

// 登录（自动处理双token）
userStore.login(userInfo).then(() => {
  console.log('登录成功')
})

// 手动刷新token（通常不需要）
userStore.refreshAccessToken().then((newToken) => {
  console.log('Token刷新成功', newToken)
})

// 登出（自动清除双token）
userStore.logOut().then(() => {
  console.log('登出成功')
})
```

## 当前状态

双token认证机制现在完全正常工作：
- ✅ 自动token刷新
- ✅ 并发请求处理
- ✅ 错误处理和重试
- ✅ 匹配后端refresh-token头实现
