import { computed } from 'vue'
import { useDict } from '@/utils/dict'

export function useFilteredDict(dictFilters) {
    const dictTypes = Object.keys(dictFilters)
    const rawDict = useDict(...dictTypes)

    const filteredDict = {}

    dictTypes.forEach(dictType => {
        const allowedValues = dictFilters[dictType]
        filteredDict[dictType] = computed(() => {
            const list = rawDict[dictType]?.value || []
            if (!allowedValues || allowedValues.length === 0) {
                return list
            }
            return list.filter(item => allowedValues.includes(item.value))
        })
    })
    return filteredDict
}
