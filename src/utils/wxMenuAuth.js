import { getUserRoles, checkMenuPermission } from "@/api/wx/menu";

/**
 * 微信小程序菜单权限工具类
 */
class WxMenuAuth {
  constructor() {
    this.userRoles = [];
    this.roleCache = null;
    this.cacheExpiry = null;
    this.cacheTimeout = 30 * 60 * 1000; // 30分钟缓存
  }

  /**
   * 获取用户角色列表
   * @returns {Promise<Array>} 用户角色列表
   */
  async getUserRoles() {
    // 检查缓存是否有效
    if (this.roleCache && this.cacheExpiry && Date.now() < this.cacheExpiry) {
      return this.roleCache;
    }

    try {
      const response = await getUserRoles();
      this.roleCache = response.data.roles || [];
      this.cacheExpiry = Date.now() + this.cacheTimeout;
      this.userRoles = this.roleCache;
      return this.roleCache;
    } catch (error) {
      console.error('获取用户角色失败:', error);
      return [];
    }
  }

  /**
   * 检查用户是否拥有指定角色
   * @param {string|Array} roles 角色或角色列表
   * @returns {Promise<boolean>} 是否拥有角色
   */
  async hasRole(roles) {
    const userRoles = await this.getUserRoles();
    
    if (typeof roles === 'string') {
      return userRoles.includes(roles) || roles === 'all';
    }
    
    if (Array.isArray(roles)) {
      return roles.some(role => userRoles.includes(role) || role === 'all');
    }
    
    return false;
  }

  /**
   * 检查用户是否有权限访问指定菜单
   * @param {number} menuId 菜单ID
   * @returns {Promise<boolean>} 是否有权限
   */
  async hasMenuPermission(menuId) {
    try {
      const response = await checkMenuPermission(menuId);
      return response.data;
    } catch (error) {
      console.error('检查菜单权限失败:', error);
      return false;
    }
  }

  /**
   * 根据用户角色过滤菜单列表
   * @param {Array} menus 菜单列表
   * @returns {Promise<Array>} 过滤后的菜单列表
   */
  async filterMenusByRole(menus) {
    const userRoles = await this.getUserRoles();
    
    return menus.filter(menu => {
      if (!menu.showFor) return true;
      
      const allowedRoles = menu.showFor.split(',').map(role => role.trim());
      
      // 如果包含 'all'，所有用户都可以访问
      if (allowedRoles.includes('all')) return true;
      
      // 检查用户是否拥有所需角色之一
      return allowedRoles.some(role => userRoles.includes(role));
    });
  }

  /**
   * 获取角色显示名称
   * @param {string} roleCode 角色代码
   * @returns {string} 角色显示名称
   */
  getRoleLabel(roleCode) {
    const roleMap = {
      'all': '所有用户',
      'general': '普通用户',
      'aider': '急救员',
      'mentor': '急救导师',
      'disciple': '弟子'
    };
    return roleMap[roleCode] || roleCode;
  }

  /**
   * 获取所有角色选项
   * @returns {Array} 角色选项列表
   */
  getRoleOptions() {
    return [
      { value: 'all', label: '所有用户' },
      { value: 'general', label: '普通用户' },
      { value: 'aider', label: '急救员' },
      { value: 'mentor', label: '急救导师' },
      { value: 'disciple', label: '弟子' }
    ];
  }

  /**
   * 检查用户是否可以申请指定角色
   * @param {string} targetRole 目标角色
   * @returns {Promise<boolean>} 是否可以申请
   */
  async canApplyForRole(targetRole) {
    const userRoles = await this.getUserRoles();
    
    // 角色升级规则
    const upgradeRules = {
      'aider': ['general'], // 普通用户可以申请急救员
      'mentor': ['general', 'aider'], // 普通用户和急救员可以申请导师
      'disciple': ['mentor'] // 导师可以收弟子
    };
    
    const allowedRoles = upgradeRules[targetRole] || [];
    return allowedRoles.some(role => userRoles.includes(role));
  }

  /**
   * 清除角色缓存
   */
  clearCache() {
    this.roleCache = null;
    this.cacheExpiry = null;
    this.userRoles = [];
  }

  /**
   * 刷新用户角色
   * @returns {Promise<Array>} 最新的用户角色列表
   */
  async refreshUserRoles() {
    this.clearCache();
    return await this.getUserRoles();
  }
}

// 创建单例实例
const wxMenuAuth = new WxMenuAuth();

export default wxMenuAuth;

/**
 * Vue 3 组合式 API Hook
 * @returns {Object} 权限相关方法
 */
export function useWxMenuAuth() {
  return {
    getUserRoles: () => wxMenuAuth.getUserRoles(),
    hasRole: (roles) => wxMenuAuth.hasRole(roles),
    hasMenuPermission: (menuId) => wxMenuAuth.hasMenuPermission(menuId),
    filterMenusByRole: (menus) => wxMenuAuth.filterMenusByRole(menus),
    getRoleLabel: (roleCode) => wxMenuAuth.getRoleLabel(roleCode),
    getRoleOptions: () => wxMenuAuth.getRoleOptions(),
    canApplyForRole: (targetRole) => wxMenuAuth.canApplyForRole(targetRole),
    clearCache: () => wxMenuAuth.clearCache(),
    refreshUserRoles: () => wxMenuAuth.refreshUserRoles()
  };
}
