<template>
  <el-container class="checkin-form-container">
    <el-main>
      <el-card class="checkin-form-card">
<!--        <div style="text-align:center;margin-bottom:20px">
          <img src="/static/logo.png" alt="logo" style="max-width:100%;max-height:50%;">
        </div>-->

        <div class="text-center" style="margin-bottom: 10px;">
          <el-tag :type="tagType" effect="dark">{{ tagLabel }}</el-tag>
        </div>

        <el-form :model="form" :rules="rules" ref="checkinForm" label-width="100px">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入姓名" />
          </el-form-item>

          <el-form-item label="身份证号" prop="id_card" :required="checkinType !== 'public'">
            <el-input v-model="form.id_card" placeholder="请输入身份证号" />
            <small style="font-size:12px;color:#888;">
              {{ checkinType === 'public' ? '公开课可不填身份证号' : '请输入身份证号' }}
            </small>
          </el-form-item>

          <el-form-item label="手机号" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入手机号" />
          </el-form-item>

          <el-form-item label="地址" prop="address">
            <el-input v-model="form.address" placeholder="请输入省份或省份城市" />
            <small style="font-size:12px;color:#888;">例如：广东省 或 广东省广州市</small>
          </el-form-item>

          <el-form-item label="邀请人" prop="inviter">
            <el-input v-model="form.inviter" placeholder="请输入邀请人" />
          </el-form-item>

          <el-form-item label="培训类型" prop="training_type">
            <el-radio-group v-model="form.training_type">
              <el-radio label="初训">初训</el-radio>
              <el-radio label="复训">复训</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSubmit">提交签到</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </el-main>
  </el-container>
</template>

<script>
import {submitUserCheckIn} from "@/api/public/check_in.js";

export default {
  name: 'CheckinForm',
  data() {
    return {
      checkinType: 'public', // 可根据实际情况传参或从URL获取
      form: {
        name: '',
        id_card: '',
        phone: '',
        address: '',
        inviter: '',
        training_type: ''
      },
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        id_card: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (this.checkinType === 'public' && !value) return callback();
              const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}[\dXx]$)/;
              if (!reg.test(value)) {
                callback(new Error('请输入正确的身份证号'));
              } else {
                callback();
              }
            }
          }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const reg = /^1[3-9]\d{9}$/;
              if (!reg.test(value)) {
                callback(new Error('请输入正确的手机号'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
        training_type: [{ required: true, message: '请选择培训类型', trigger: 'change' }]
      }
    };
  },
  computed: {
    tagLabel() {
      switch (this.checkinType) {
        case 'public': return '公开课';
        case 'practice': return '实操培训课';
        case 'mentor': return '导师班';
        default: return '';
      }
    },
    tagType() {
      switch (this.checkinType) {
        case 'public': return 'success';
        case 'practice': return 'primary';
        case 'mentor': return 'danger';
        default: return 'info';
      }
    }
  },
  mounted() {
    const saved = JSON.parse(localStorage.getItem('checkinData') || '{}');
    Object.assign(this.form, saved);
  },
  methods: {
    handleSubmit() {
      this.$refs.checkinForm.validate(valid => {
        if (!valid) return;
        const payload = { ...this.form, checkin_type: this.checkinType };
        localStorage.setItem('checkinData', JSON.stringify(this.form));

        fetch('/checkin', {
          method: 'POST',
          body: new URLSearchParams(payload)
        })
            .then(res => res.json())
            .then(data => {
              if (data.status === 'success') {
                this.$message.success(data.message);
                this.$refs.checkinForm.resetFields();
              } else {
                this.$message.error(data.message || '签到失败');
              }
            })
            .catch(() => {
              this.$message.error('签到失败');
            });
      });
    }
  }
};
</script>

<style scoped>
.checkin-form-container {
  background-color: #f5f5f5;
  padding: 30px 10px;
}

.checkin-form-card {
  max-width: 500px;
  margin: auto;
  padding: 20px;
  border-radius: 10px;
}
</style>
