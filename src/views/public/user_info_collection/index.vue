<template>
  <div class="container py-4 px-3">
    <div class="form-container bg-white p-3 rounded shadow-sm" style="max-width: 100%; width: 100%;">
      <h3 class="text-center mb-3">用户信息收集</h3>

      <el-alert
          v-if="showSuccess"
          title="提交成功！"
          type="success"
          show-icon
          center
          class="mb-2"
      />

      <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="top"
          class="form-responsive"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名"/>
        </el-form-item>

        <el-form-item label="性别" prop="sex">
          <el-radio-group v-model="form.sex">
            <el-radio label="0">男</el-radio>
            <el-radio label="1">女</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号"/>
        </el-form-item>

        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号"/>
        </el-form-item>

        <el-form-item label="一同邮寄人">
          <el-input v-model="form.companions" placeholder="例如：张三, 李四"/>
        </el-form-item>

        <el-form-item label="邮寄地址" prop="address">
          <el-input
              type="textarea"
              v-model="form.address"
              :rows="3"
              placeholder="请输入邮寄地址"
          />
        </el-form-item>

        <div class="text-center">
          <el-button type="primary"
                     :loading="isSubmitting"
                     :disabled="isSubmitting"
                     @click="onSubmit">提交</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script setup>
import {ref, getCurrentInstance} from 'vue'
import {submitUserInfo} from '@/api/public/user_info_collection'

const {proxy} = getCurrentInstance()

const formRef = ref(null)

const isSubmitting = ref(false)

const form = ref({
  name: '',
  sex: '',
  phone: '',
  idCard: '',
  companions: '',
  address: ''
})

const showSuccess = ref(false)

const rules = {
  name: [{required: true, message: '请输入姓名', trigger: 'blur'}],
  gender: [{required: true, message: '请选择性别', trigger: 'change'}],
  sex: [{required: true, message: '请选择性别', trigger: 'change'}],
  phone: [
    {required: true, message: '请输入手机号', trigger: 'blur'},
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号',
      trigger: 'blur'
    }
  ],
  idCard: [
    {required: true, message: '请输入身份证号', trigger: 'blur'},
    {
      pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
      message: '请输入正确的身份证号',
      trigger: 'blur'
    }
  ],
  address: [{required: true, message: '请输入地址', trigger: 'blur'}]
}

const onSubmit = () => {
  if (isSubmitting.value) return
  formRef.value.validate((valid) => {
    if (!valid) return
    isSubmitting.value = true // 启动节
    submitUserInfo(form.value)
        .then((res) => {
          if (res.code === 200) {
            showSuccess.value = true
            setTimeout(() => {
              showSuccess.value = false
            }, 2000)
            // 重置表单
            form.value = {
              name: '',
              gender: '',
              phone: '',
              idCard: '',
              companions: '',
              address: ''
            }
            formRef.value.resetFields()
          } else {
            proxy.$modal.msgError(res.message || '提交失败，请重试')
          }
        })
        .catch(() => {
          proxy.$modal.msgError('提交失败，请重试')
        })
        .finally(() => {
          isSubmitting.value = false
        })
  })
}
</script>
<style scoped>
.form-container {
  max-width: 600px;
  width: 100%;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .form-container {
    padding: 1rem;
    box-shadow: none;
  }

  .el-form-item__label {
    font-size: 14px;
  }

  .el-input__inner,
  textarea {
    font-size: 14px;
  }

  .el-button {
    width: 100%;
  }
}
</style>
