<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12" style="padding-left: 20px">
        <h2>忻道-中医急救三分钟管理系统</h2>
        <p>
          本系统致力于提供高效、专业的中医急救管理解决方案。通过现代化的管理平台，实现急救流程的标准化、规范化，为中医急救提供强有力的技术支持。系统集成了急救知识库、人员管理、设备管理、急救记录等核心功能，帮助医疗机构提升急救效率，保障患者生命安全。
        </p>
        <p>
          <b>当前版本:</b> <span>v{{ version }}</span>
        </p>
        <p>
          <el-tag type="success">中医生命急救三分钟管理平台</el-tag>
        </p>
      </el-col>

      <el-col :sm="24" :lg="12" style="padding-left: 50px">
        <el-row>
          <el-col :span="12">
            <h2>系统功能</h2>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <h4>核心功能</h4>
            <ul>
              <li>急救流程管理</li>
              <li>人员资质管理</li>
              <li>设备物资管理</li>
              <li>急救记录追踪</li>
              <li>知识库管理</li>
              <li>统计分析</li>
              <li>...</li>
            </ul>
          </el-col>
          <el-col :span="6">
            <h4>技术特点</h4>
            <ul>
              <li>实时响应</li>
              <li>数据安全</li>
              <li>操作便捷</li>
              <li>多端协同</li>
              <li>智能提醒</li>
              <li>数据可视化</li>
              <li>...</li>
            </ul>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-divider />
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <template v-slot:header>
            <div class="clearfix">
              <span>急救指南</span>
            </div>
          </template>
          <div class="body">
            <p>
              <i class="el-icon-warning"></i> 紧急情况：<el-link
                href="/emergency"
                target="_blank"
                >查看急救流程</el-link
              >
            </p>
            <p>
              <i class="el-icon-document"></i> 急救知识：<el-link
                href="/knowledge"
                target="_blank"
                >访问知识库</el-link
              >
            </p>
            <p>
              <i class="el-icon-phone"></i> 急救热线：<a
                href="#"
                >4006-911-995</a
              >
            </p>
            <p>
              <i class="el-icon-location"></i> 最近医院：<a
                href="javascript:;"
                >查看地图</a
              >
            </p>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <template v-slot:header>
            <div class="clearfix">
              <span>系统公告</span>
            </div>
          </template>
          <el-collapse accordion>
            <el-collapse-item title="系统更新 - 2024-03-20">
              <ol>
                <li>新增急救流程标准化管理</li>
                <li>优化急救记录追踪功能</li>
                <li>完善知识库检索系统</li>
                <li>新增设备物资预警功能</li>
                <li>优化数据统计分析报表</li>
                <li>提升系统响应速度</li>
              </ol>
            </el-collapse-item>
            <el-collapse-item title="使用指南 - 2024-03-15">
              <ol>
                <li>急救流程操作指南</li>
                <li>设备使用说明</li>
                <li>数据录入规范</li>
                <li>系统权限说明</li>
              </ol>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <template v-slot:header>
            <div class="clearfix">
              <span>快速入口</span>
            </div>
          </template>
          <div class="body">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-button type="primary" plain icon="FirstAidKit" @click="goTarget('/infomanage/course')">课程管理</el-button>
              </el-col>
              <el-col :span="8">
                <el-button type="success" plain icon="User" @click="goTarget('/infomanage/member_info')">人员管理</el-button>
              </el-col>
              <el-col :span="8">
                <el-button type="warning" plain icon="Box" @click="goTarget('/infomanage/apply')">证书申请</el-button>
              </el-col>
            </el-row>
            <el-row :gutter="10" style="margin-top: 10px">
              <el-col :span="8">
                <el-button type="info" plain icon="Document" @click="goTarget('/infomanage/course_attendance')">签到记录</el-button>
              </el-col>
              <el-col :span="8">
                <el-button type="danger" plain icon="DataLine" @click="goTarget('/infomanage/course_enrollment')">报名记录</el-button>
              </el-col>
              <el-col :span="8">
                <el-button type="primary" plain icon="Setting" @click="goTarget('/infomanage/user_info_collection')">证件邮寄信息</el-button>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Index">
const version = ref('1.0.0')

function goTarget(url) {
  window.open(url, '__blank')
}
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  .el-button {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>

