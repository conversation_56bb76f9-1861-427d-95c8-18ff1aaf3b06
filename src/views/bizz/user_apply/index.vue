<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="申请状态" prop="applyStatus">
        <el-select v-model="queryParams.applyStatus" placeholder="请选择申请状态" clearable>
          <el-option
            v-for="dict in apply_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户身份" prop="userType">
        <el-select v-model="queryParams.userType" placeholder="请选择用户身份" clearable>
          <el-option
            v-for="dict in sys_user_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="真实姓名" prop="realName">
        <el-input
          v-model="queryParams.realName"
          placeholder="请输入真实姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="邀请人姓名" prop="inviterName">
        <el-input
          v-model="queryParams.inviterName"
          placeholder="请输入邀请人姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['bizz:user_apply:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Check"
          :disabled="!allSelectedStatus2"
          @click="handleApprove(null,'pass')"
          v-hasPermi="['bizz:user_apply:edit']"
        >批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Close"
          :disabled="!allSelectedStatus2"
          @click="handleApprove(null,'reject')"
          v-hasPermi="['bizz:user_apply:edit']"
        >批量拒绝</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['bizz:user_apply:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="user_applyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="真实姓名" align="center" prop="realName" fixed="left"/>
      <el-table-column label="申请状态" align="center" prop="applyStatus">
        <template #default="scope">
          <dict-tag :options="apply_status" :value="scope.row.applyStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="用户身份" align="center" prop="userType">
        <template #default="scope">
          <dict-tag :options="sys_user_type" :value="scope.row.userType"/>
        </template>
      </el-table-column>
      <el-table-column label="证件类型" align="center" prop="cardType">
        <template #default="scope">
          <dict-tag :options="sys_card_type" :value="scope.row.cardType"/>
        </template>
      </el-table-column>
      <el-table-column label="证件号码" align="center" prop="idCard" show-overflow-tooltip />
      <el-table-column label="性别" align="center" prop="sex">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.sex"/>
        </template>
      </el-table-column>
      <el-table-column label="邀请人ID" align="center" prop="inviterId" />
      <el-table-column label="邀请人姓名" align="center" prop="inviterName" />
      <el-table-column label="照片地址" align="center" prop="photoUrl">
        <template #default="scope">
          <image-preview :src="scope.row.photoUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="primary"
            text
            size="small"
            :disabled="scope.row.applyStatus != 2"
            @click="handleApprove(scope.row.id, 'pass')"
          >通过</el-button>
          <el-button
            type="primary"
            text
            size="small"
            :disabled="scope.row.applyStatus != 2"
            @click="handleApprove(scope.row.id, 'reject')"
          >拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户身份申请对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="user_applyRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请状态" prop="applyStatus">
          <el-select v-model="form.applyStatus" placeholder="请选择申请状态">
            <el-option
              v-for="dict in apply_status"
              :key="dict.value"
              :label="dict.label"
:value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户身份" prop="userType">
          <el-select v-model="form.userType" placeholder="请选择用户身份">
            <el-option
              v-for="dict in sys_user_type"
              :key="dict.value"
              :label="dict.label"
:value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="form.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="证件类型" prop="cardType">
          <el-select v-model="form.cardType" placeholder="请选择证件类型">
            <el-option
              v-for="dict in sys_card_type"
              :key="dict.value"
              :label="dict.label"
:value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证件号码" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入证件号码" />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="form.sex" placeholder="请选择性别">
            <el-option
              v-for="dict in sys_user_sex"
              :key="dict.value"
              :label="dict.label"
:value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="手机号码" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="邀请人ID" prop="inviterId">
          <el-input v-model="form.inviterId" placeholder="请输入邀请人ID" />
        </el-form-item>
        <el-form-item label="邀请人姓名" prop="inviterName">
          <el-input v-model="form.inviterName" placeholder="请输入邀请人姓名" />
        </el-form-item>
        <el-form-item label="照片id" prop="photoId">
          <el-input v-model="form.photoId" placeholder="请输入照片id" />
        </el-form-item>
        <el-form-item label="照片地址" prop="photoUrl">
          <el-input v-model="form.photoUrl" placeholder="请输入照片地址" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User_apply">
import { listUser_apply, getUser_apply, delUser_apply, addUser_apply, updateUser_apply, auditUser_apply } from "@/api/bizz/user_apply";

const { proxy } = getCurrentInstance();
const { apply_status, sys_user_type, sys_user_sex, sys_card_type } = proxy.useDict('apply_status', 'sys_user_type', 'sys_user_sex', 'sys_card_type');

const user_applyList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 计算属性：所有选中项的申请状态是否都是2（待审核）
const allSelectedStatus2 = computed(() => {
  return ids.value.length > 0 &&
    user_applyList.value.filter(item => ids.value.includes(item.id)).every(item => item.applyStatus == 2);
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    applyStatus: null,
    userType: null,
    realName: null,
    inviterName: null,
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户身份申请列表 */
function getList() {
  loading.value = true;
  listUser_apply(queryParams.value).then(response => {
    user_applyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    applyStatus: null,
    userType: null,
    realName: null,
    cardType: null,
    idCard: null,
    sex: null,
    phoneNumber: null,
    inviterId: null,
    inviterName: null,
    photoId: null,
    photoUrl: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null
  };
  proxy.resetForm("user_applyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加用户身份申请";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getUser_apply(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改用户身份申请";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["user_applyRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateUser_apply(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addUser_apply(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除用户身份申请编号为"' + _ids + '"的数据项？').then(function() {
    return delUser_apply(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bizz/user_apply/export', {
    ...queryParams.value
  }, `user_apply_${new Date().getTime()}.xlsx`)
}

/** 审批通过/拒绝操作 */
function handleApprove(id, type) {
  // type: 'pass' or 'reject'
  const _ids = id || ids.value
  proxy.$modal.confirm(`是否确认${type === 'pass' ? '审批通过' : '审批拒绝'}用户身份申请？`).then(function () {
    // 调用auditUser_apply方法，传入id和type
    return auditUser_apply(_ids, type);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(`${type === 'pass' ? '审批通过' : '审批拒绝'}成功`);
  }).catch(() => {});
}

getList();
</script>
