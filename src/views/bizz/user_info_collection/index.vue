<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input
          v-model="queryParams.idCard"
          placeholder="请输入身份证号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一同邮寄人" prop="companions" label-width="auto">
        <el-input
          v-model="queryParams.companions"
          placeholder="请输入同行人姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
            v-model="queryParams.createTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            clearable
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="collectionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="性别" align="center" prop="sex" >
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.sex" />
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="身份证号" align="center" prop="idCard" />
      <el-table-column label="邮寄地址" align="center" prop="address" width="200" show-overflow-tooltip/>
      <el-table-column label="一同邮寄人" align="center" prop="companions" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户信息收集对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="collectionRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="${comment}" prop="name">
          <el-input v-model="form.name" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="address">
          <el-input v-model="form.address" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="companions">
          <el-input v-model="form.companions" placeholder="请输入${comment}" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Collection">
import { listCollection, getCollection, delCollection, addCollection, updateCollection } from "@/api/bizz/user_info_collection.js"
const { proxy } = getCurrentInstance()
const { sys_user_sex} = proxy.useDict("sys_user_sex");

const collectionList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    idCard: null,
    address: null,
    companions: null,
    createTime: [],  // 新增字段：日期范围

  },
  rules: {
    name: [
      { required: true, message: "$comment不能为空", trigger: "blur" }
    ],
    phone: [
      { required: true, message: "$comment不能为空", trigger: "blur" }
    ],
    idCard: [
      { required: true, message: "$comment不能为空", trigger: "blur" }
    ],
    address: [
      { required: true, message: "$comment不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询用户信息收集列表 */
function getList() {
  loading.value = false
  let time = queryParams.value.createTime;
  let {pageNum, pageSize, name, idCard,companions} = queryParams.value;
  const params = {
    pageNum,
    pageSize,
    name,
    idCard,
    companions,
    beginTime: time?.[0] || null,
    endTime: time?.[1] || null
  }
  console.log(params)
  listCollection(params).then(response => {
    collectionList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    sex: null,
    phone: null,
    idCard: null,
    address: null,
    companions: null,
    createTime: null
  }
  proxy.resetForm("collectionRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加用户信息收集"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getCollection(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改用户信息收集"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["collectionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateCollection(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addCollection(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除该用户信息？').then(function() {
    return delCollection(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  let time = queryParams.value.createTime;
  let {pageNum, pageSize, name, idCard,companions} = queryParams.value;
  proxy.download('bizz/user_info_collection/export', {
    pageNum,
    pageSize,
    name,
    idCard,
    companions,
    beginTime: time?.[0] || null,
    endTime: time?.[1] || null
  }, `collection_${new Date().getTime()}.xlsx`)
}

getList()
</script>
