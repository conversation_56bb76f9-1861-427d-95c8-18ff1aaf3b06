<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="二维码用途说明" prop="description" label-width="auto">
        <el-input
          v-model="queryParams.description"
          placeholder="请输入二维码用途说明"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['bizz:qr_code:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 卡片式二维码列表 -->
    <el-row :gutter="20">
      <el-col v-for="item in qr_codeList" :key="item.id" :span="6">
        <el-card shadow="hover">
          <div class="qr-code-card">
            <p><strong>用途说明：</strong>{{ item.description }}</p>
            <p><strong>内容：</strong>{{ item.content }}</p>
            <p><strong>二维码：</strong>{{ item.logoFilename }}</p>
            <div v-if="item.qrCodeBase64" style="text-align:center;margin:10px 0;">
              <img :src="item.qrCodeBase64" alt="二维码" style="max-width: 200px;" />
            </div>
            <div style="text-align: right;">
              <el-button
                  type="text"
                  icon="Edit"
                  @click="handleUpdate(item)"
                  v-hasPermi="['bizz:qr_code:edit']"
              >修改</el-button>
              <el-button
                  type="text"
                  icon="Delete"
                  @click="handleDelete(item)"
                  v-hasPermi="['bizz:qr_code:remove']"
              >删除</el-button>
              <el-button
                  type="text"
                  icon="Download"
                  @click="handleDownload(item)"
                  v-hasPermi="['bizz:qr_code:download']"
              >下载</el-button>
              <el-button
                  type="text"
                  icon="View"
                  @click="handleCopyContent(item)"
                  v-hasPermi="['bizz:qr_code:preview']"
              >复制内容</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>


    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改二维码信息对话框 -->
    <el-dialog :title="title" v-model="open" width="50%" append-to-body>
      <el-form ref="qr_codeRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="二维码内容" prop="content" label-width="auto">
          <editor v-model="form.content" :min-height="192"/>
        </el-form-item>
        <el-form-item label="二维码用途说明" prop="description" label-width="auto">
          <el-input v-model="form.description" placeholder="请输入二维码用途说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Qr_code">
import { listQr_code, getQr_code, delQr_code, addQr_code, updateQr_code } from "@/api/bizz/qr_code";

const { proxy } = getCurrentInstance();

const qr_codeList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    content: null,
    description: null,
    logoFilename: null,
    qrCodeBase64: null,
  },
  rules: {
    content: [
      { required: true, message: "二维码内容不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询二维码信息列表 */
function getList() {
  loading.value = true;
  listQr_code(queryParams.value).then(response => {
    qr_codeList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    content: null,
    description: null,
    logoFilename: null,
    qrCodeBase64: null,
    createBy: null
  };
  proxy.resetForm("qr_codeRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加二维码信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getQr_code(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改二维码信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["qr_codeRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateQr_code(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addQr_code(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除该二维码？').then(function() {
    return delQr_code(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleDownload(item){
  // 创建一个 a 标签用于下载
  const link = document.createElement('a');
  link.href = item.qrCodeBase64;
  link.download = `qr_code_${item.description}.png`; // 设置下载的文件名
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

function handleCopyContent(item){
  // 去除标签
  const content = item.content.replace(/<[^>]+>/g, "");
  navigator.clipboard.writeText(content).then(() => {
    proxy.$modal.msgSuccess("复制成功");
  }).catch(() => {
    proxy.$modal.msgError("复制失败");
  });
}

getList();
</script>

<style scoped>
.qr-code-card p {
  margin: 6px 0;
  font-size: 14px;
  color: #444;
}
</style>
