<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行为编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入行为编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行为名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入行为名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否均分" prop="averageFlag">
        <el-select v-model="queryParams.averageFlag" placeholder="请选择是否均分" clearable>
          <el-option
            v-for="dict in sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['bizz:contribution_action_config:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bizz:contribution_action_config:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bizz:contribution_action_config:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['bizz:contribution_action_config:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="contribution_action_configList" @selection-change="handleSelectionChange">
      <el-table-column label="行为编码" align="center" prop="code" />
      <el-table-column label="行为名称" align="center" prop="name" />
      <el-table-column label="基础分值" align="center" prop="score" />
      <el-table-column label="是否均分" align="center" prop="averageFlag">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.averageFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="状态" align="center" prop="enabled">
        <template #default="scope">
          <el-switch
              v-model="scope.row.enabled"
              active-value="1"
              active-text="启用"
              inline-prompt
              inactive-text="禁用"
              inactive-value="0"
              @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['bizz:contribution_action_config:edit']"
          >修改</el-button>
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['bizz:contribution_action_config:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改贡献值配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="contribution_action_configRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="行为编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入行为编码" />
        </el-form-item>
        <el-form-item label="行为名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入行为名称" />
        </el-form-item>
        <el-form-item label="基础分值" prop="score">
          <el-input v-model="form.score" placeholder="请输入基础分值" />
        </el-form-item>
        <el-form-item label="是否均分" prop="averageFlag">
          <el-select v-model="form.averageFlag" placeholder="请选择是否均分">
            <el-option
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="是否启用" prop="enabled">
          <el-switch v-model="form.enabled" active-text="启用" inline-prompt active-value="1" inactive-value="0" inactive-text="禁用" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { listContribution_action_config, getContribution_action_config, delContribution_action_config, addContribution_action_config, updateContribution_action_config } from "@/api/bizz/contribution_action_config";

const { proxy } = getCurrentInstance();
const { sys_yes_no } = proxy.useDict('sys_yes_no');

const contribution_action_configList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    code: null,
    name: null,
    averageFlag: null,
    enabled: null,
  },
  rules: {
    code: [
      { required: true, message: "行为编码不能为空", trigger: "blur" }
    ],
    name: [
      { required: true, message: "行为名称不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询贡献值配置列表 */
function getList() {
  loading.value = true;
  listContribution_action_config(queryParams.value).then(response => {
    contribution_action_configList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    code: null,
    name: null,
    score: null,
    averageFlag: null,
    remark: null,
    enabled: "1",
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null
  };
  proxy.resetForm("contribution_action_configRef");
  // 重置表单后再次设置默认值，确保新增时启用状态为默认启用
  form.value.enabled = "1";
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加贡献值配置";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getContribution_action_config(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改贡献值配置";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["contribution_action_configRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateContribution_action_config(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addContribution_action_config(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除贡献值配置编号为"' + _ids + '"的数据项？').then(function() {
    return delContribution_action_config(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bizz/contribution_action_config/export', {
    ...queryParams.value
  }, `contribution_action_config_${new Date().getTime()}.xlsx`)
}

const handleStatusChange = (row) => {
  const enabled = row.enabled
  updateContribution_action_config({ ...row, enabled }).then(() => {
    proxy.$modal.msgSuccess("状态更新成功");
    getList();
  }).catch(() => {
    row.status = !row.status; // 恢复原状态
  });
};

getList();
</script>
