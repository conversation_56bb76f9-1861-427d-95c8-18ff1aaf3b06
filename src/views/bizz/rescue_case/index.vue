<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="急救日期" prop="rescueDate">
        <el-date-picker clearable
          v-model="queryParams.rescueDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择急救日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="急救所在城市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入急救所在城市"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被救人姓名" prop="patientName">
        <el-input
          v-model="queryParams.patientName"
          placeholder="请输入被救人姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被救人性别" prop="patientGender">
        <el-select v-model="queryParams.patientGender" placeholder="请选择被救人性别" clearable style="width: 200px">
          <el-option label="男" value="0" />
          <el-option label="女" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="病症类型" prop="illnessType">
        <el-select v-model="queryParams.illnessType" placeholder="请选择病症类型" clearable style="width: 200px">
          <el-option
            v-for="dict in rescue_disease_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="案例状态" prop="rescueStatus">
        <el-select v-model="queryParams.rescueStatus" placeholder="请选择急救案例状态" clearable>
          <el-option
            v-for="dict in apply_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['bizz:rescue_case:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bizz:rescue_case:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bizz:rescue_case:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['bizz:rescue_case:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleBatchApprove('pass')"
          v-hasPermi="['bizz:rescue_case:audit']"
        >批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Close"
          :disabled="multiple"
          @click="handleBatchApprove('reject')"
          v-hasPermi="['bizz:rescue_case:audit']"
        >批量拒绝</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="rescue_caseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="expand" label="急救员" width="80">
        <template #default="props">
          <el-table :data="props.row.rescuers">
            <el-table-column label="姓名" prop="rescuerName" width="150" align="center"/>
            <el-table-column label="手机号" prop="rescuerPhone" width="200" align="center" />
          </el-table>
        </template>
      </el-table-column>
      <el-table-column label="案例状态" align="center" prop="rescueStatus" width="100">
        <template #default="scope">
          <dict-tag :options="apply_status" :value="scope.row.rescueStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="急救日期" align="center" prop="rescueDate" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.rescueDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="急救所在城市" align="center" prop="city" width="180"/>
      <el-table-column label="详细地址" align="center" prop="address" show-overflow-tooltip/>
      <el-table-column label="被救人姓名" align="center" prop="patientName" />
      <el-table-column label="被救人性别" align="center" prop="patientGender">
        <template #default="scope">
          <dict-tag :options="[{value: '0', label: '男'}, {value: '1', label: '女'}]" :value="scope.row.patientGender"/>
        </template>
      </el-table-column>
      <el-table-column label="被救人年龄" align="center" prop="patientAge" />
      <el-table-column label="病症类型" align="center" prop="illnessType">
        <template #default="scope">
          <dict-tag :options="rescue_disease_type" :value="scope.row.illnessType"/>
        </template>
      </el-table-column>
      <el-table-column label="是否远程指导" align="center" prop="onlineFlag" width="120">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.onlineFlag === '1'">是</el-tag>
          <el-tag type="info" v-else>否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="远程指导员姓名" align="center" prop="remoteGuideRealName" width="120"/>
      <el-table-column label="急救情况说明" align="center" prop="rescueDescription" show-overflow-tooltip/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <el-button
              type="primary"
              text
              size="small"
              :disabled="scope.row.rescueStatus != 2"
              @click="handleApprove(scope.row.id, 'pass')"
          >通过</el-button>
          <el-button
              type="primary"
              text
              size="small"
              :disabled="scope.row.rescueStatus != 2"
              @click="handleApprove(scope.row.id, 'reject')"
          >拒绝</el-button>
          <AttachmentViewer
            :attachments="scope.row.attachments"
            mode="button"
            :config="rescueAttachmentConfig"
          />
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改急救案例对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body draggable>
      <el-form ref="rescue_caseRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="急救日期" prop="rescueDate">
          <el-date-picker clearable
            v-model="form.rescueDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择急救日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="急救所在城市" prop="city">
          <el-input v-model="form.city" placeholder="请输入急救所在城市" />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" />
        </el-form-item>
        <el-form-item label="被救人姓名" prop="patientName">
          <el-input v-model="form.patientName" placeholder="请输入被救人姓名" />
        </el-form-item>
        <el-form-item label="被救人性别" prop="patientGender">
          <el-input v-model="form.patientGender" placeholder="请输入被救人性别" />
        </el-form-item>
        <el-form-item label="被救人年龄" prop="patientAge">
          <el-input v-model="form.patientAge" placeholder="请输入被救人年龄" />
        </el-form-item>
        <el-form-item label="病症类型" prop="illnessType">
          <el-input v-model="form.illnessType" placeholder="请输入病症类型" />
        </el-form-item>
        <el-form-item label="急救情况说明" prop="rescueDescription">
          <el-input v-model="form.rescueDescription" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup>
import { listRescue_case, getRescue_case, delRescue_case, addRescue_case, updateRescue_case, batchAuditRescueCase } from "@/api/bizz/rescue_case";
import { VideoPlay } from '@element-plus/icons-vue';
import AttachmentViewer from '@/components/AttachmentViewer/index.vue';

const { proxy } = getCurrentInstance();
const { apply_status, rescue_disease_type } = proxy.useDict('apply_status', 'rescue_disease_type');

const rescue_caseList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");


// 急救案例附件配置
const rescueAttachmentConfig = {
  urlField: 'fileUrl',        // 急救案例使用fileUrl字段
  typeField: 'fileType',      // 急救案例使用fileType字段
  imageType: 'image',         // 图片类型值
  videoType: 'video'          // 视频类型值
};

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    rescueDate: null,
    city: null,
    address: null,
    patientName: null,
    patientGender: null,
    patientAge: null,
    illnessType: null,
    rescueDescription: null,
    rescueStatus: null,
  },
  rules: {
    rescueDate: [
      { required: true, message: "急救日期不能为空", trigger: "blur" }
    ],
    city: [
      { required: true, message: "急救所在城市不能为空", trigger: "blur" }
    ],
    address: [
      { required: true, message: "详细地址不能为空", trigger: "blur" }
    ],
    patientName: [
      { required: true, message: "被救人姓名不能为空", trigger: "blur" }
    ],
    patientGender: [
      { required: true, message: "被救人性别不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询急救案例列表 */
function getList() {
  loading.value = true;
  listRescue_case(queryParams.value).then(response => {
    rescue_caseList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    rescueDate: null,
    city: null,
    address: null,
    patientName: null,
    patientGender: null,
    patientAge: null,
    illnessType: null,
    rescueDescription: null,
    rescueStatus: '1',
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null
  };
  proxy.resetForm("rescue_caseRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加急救案例";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getRescue_case(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改急救案例";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["rescue_caseRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateRescue_case(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addRescue_case(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除急救案例编号为"' + _ids + '"的数据项？').then(function() {
    return delRescue_case(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bizz/rescue_case/export', {
    ...queryParams.value
  }, `rescue_case_${new Date().getTime()}.xlsx`)
}

/** 单个审批操作 */
function handleApprove(id, action) {
  const statusMap = {
    'pass': '3',    // 通过
    'reject': '4'   // 拒绝
  }
  const actionText = action === 'pass' ? '通过' : '拒绝'

  proxy.$modal.confirm(`确认${actionText}该急救案例？`).then(() => {
    return batchAuditRescueCase([id], statusMap[action])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(`${actionText}成功`)
  }).catch(() => {})
}

/** 批量审批操作 */
function handleBatchApprove(action) {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning("请选择要审批的数据项")
    return
  }

  const statusMap = {
    'pass': '3',    // 通过
    'reject': '4'   // 拒绝
  }
  const actionText = action === 'pass' ? '通过' : '拒绝'

  proxy.$modal.confirm(`确认批量${actionText}选中的 ${ids.value.length} 个急救案例？`).then(() => {
    return batchAuditRescueCase(ids.value, statusMap[action])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(`批量${actionText}成功`)
  }).catch(() => {})
}



getList();
</script>


