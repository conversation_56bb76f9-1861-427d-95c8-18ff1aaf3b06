<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="活动主题" prop="activityTitle">
        <el-input
          v-model="queryParams.activityTitle"
          placeholder="请输入活动主题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker clearable
          v-model="queryParams.startDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择开始日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker clearable
          v-model="queryParams.endDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审核单状态" prop="reviewStatus">
        <el-select v-model="queryParams.reviewStatus" placeholder="请选择审核单状态" clearable>
          <el-option
            v-for="dict in apply_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['bizz:activity_review:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bizz:activity_review:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bizz:activity_review:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['bizz:activity_review:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleBatchApprove('pass')"
          v-hasPermi="['bizz:activity_review:audit']"
        >批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Close"
          :disabled="multiple"
          @click="handleBatchApprove('reject')"
          v-hasPermi="['bizz:activity_review:audit']"
        >批量拒绝</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="activity_reviewList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="expand" label="组织者" width="80">
        <template #default="props">
          <el-table :data="getMergedOrganizers(props.row)">
            <el-table-column label="姓名" prop="userName" width="150" align="center"/>
            <el-table-column label="角色" prop="roleTypeText" width="100" align="center" />
            <el-table-column label="用户ID" prop="userId" width="100" align="center" />
          </el-table>
        </template>
      </el-table-column>
      <el-table-column label="活动主题" align="center" prop="activityTitle" />
      <el-table-column label="开始日期" align="center" prop="startDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束日期" align="center" prop="endDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="讲师姓名" align="center" prop="lecturerName" />
      <el-table-column label="讲师联系方式" align="center" prop="lecturerPhone" />
      <el-table-column label="活动人数" align="center" prop="attendeeCount" />
      <el-table-column label="活动说明" align="center" prop="activityDescription" show-overflow-tooltip />
      <el-table-column label="审核单状态" align="center" prop="reviewStatus">
        <template #default="scope">
          <dict-tag :options="apply_status" :value="scope.row.reviewStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <el-button
            type="primary"
            text
            size="small"
            :disabled="scope.row.reviewStatus != 2"
            @click="handleApprove(scope.row.id, 'pass')"
            v-hasPermi="['bizz:activity_review:audit']"
          >通过</el-button>
          <el-button
            type="primary"
            text
            size="small"
            :disabled="scope.row.reviewStatus != 2"
            @click="handleApprove(scope.row.id, 'reject')"
            v-hasPermi="['bizz:activity_review:audit']"
          >拒绝</el-button>
          <AttachmentViewer
            :attachments="scope.row.attachments"
            mode="button"
          />
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改活动审核对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body draggable>
      <el-form ref="activity_reviewRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="活动主题" prop="activityTitle">
          <el-input v-model="form.activityTitle" placeholder="请输入活动主题" />
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker clearable
            v-model="form.startDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker clearable
            v-model="form.endDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="讲师姓名" prop="lecturerName">
          <el-input v-model="form.lecturerName" placeholder="请输入讲师姓名" />
        </el-form-item>
        <el-form-item label="讲师联系方式" prop="lecturerPhone">
          <el-input v-model="form.lecturerPhone" placeholder="请输入讲师联系方式" />
        </el-form-item>
        <el-form-item label="活动人数" prop="attendeeCount">
          <el-input v-model="form.attendeeCount" placeholder="请输入活动人数" />
        </el-form-item>
        <el-form-item label="活动说明" prop="activityDescription">
          <el-input v-model="form.activityDescription" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup>
import { listActivity_review, getActivity_review, delActivity_review, addActivity_review, updateActivity_review, batchAuditActivityReview } from "@/api/bizz/activity_review";
import { VideoPlay } from '@element-plus/icons-vue';
import AttachmentViewer from '@/components/AttachmentViewer/index.vue';

const { proxy } = getCurrentInstance();
const { apply_status } = proxy.useDict('apply_status');

const activity_reviewList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");


const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    activityTitle: null,
    startDate: null,
    endDate: null,
    reviewStatus: null,
  },
  rules: {
    activityTitle: [
      { required: true, message: "活动主题不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询活动审核列表 */
function getList() {
  loading.value = true;
  listActivity_review(queryParams.value).then(response => {
    activity_reviewList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    activityTitle: null,
    startDate: null,
    endDate: null,
    lecturerName: null,
    lecturerPhone: null,
    attendeeCount: null,
    activityDescription: null,
    reviewStatus: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null
  };
  proxy.resetForm("activity_reviewRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加活动审核";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getActivity_review(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改活动审核";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["activity_reviewRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateActivity_review(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addActivity_review(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除活动审核编号为"' + _ids + '"的数据项？').then(function() {
    return delActivity_review(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bizz/activity_review/export', {
    ...queryParams.value
  }, `activity_review_${new Date().getTime()}.xlsx`)
}

/** 单个审批操作 */
function handleApprove(id, action) {
  const statusMap = {
    'pass': '3',    // 通过
    'reject': '4'   // 拒绝
  }
  const actionText = action === 'pass' ? '通过' : '拒绝'

  proxy.$modal.confirm(`确认${actionText}该活动审核？`).then(() => {
    return batchAuditActivityReview([id], statusMap[action])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(`${actionText}成功`)
  }).catch(() => {})
}

/** 批量审批操作 */
function handleBatchApprove(action) {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning("请选择要审批的数据项")
    return
  }

  const statusMap = {
    'pass': '3',    // 通过
    'reject': '4'   // 拒绝
  }
  const actionText = action === 'pass' ? '通过' : '拒绝'

  proxy.$modal.confirm(`确认批量${actionText}选中的 ${ids.value.length} 个活动审核？`).then(() => {
    return batchAuditActivityReview(ids.value, statusMap[action])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(`批量${actionText}成功`)
  }).catch(() => {})
}



/** 合并organizers和assistants数据 */
function getMergedOrganizers(row) {
  const merged = [];

  // 添加主办人 (roleType = 0)
  if (row.organizers && Array.isArray(row.organizers)) {
    row.organizers.forEach(organizer => {
      merged.push({
        ...organizer,
        roleType: 0,
        roleTypeText: '主办'
      });
    });
  }

  // 添加协办人 (roleType = 1)
  if (row.assistants && Array.isArray(row.assistants)) {
    row.assistants.forEach(assistant => {
      merged.push({
        ...assistant,
        roleType: 1,
        roleTypeText: '协办'
      });
    });
  }

  return merged;
}

getList();
</script>


