<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="课程名称" prop="courseName">
        <el-input
            v-model="queryParams.courseName"
            placeholder="请输入课程名称"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程状态" prop="courseStatus">
        <el-select v-model="queryParams.courseStatus" placeholder="请选择课程状态" clearable>
          <el-option
              v-for="dict in course_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['bizz:course:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['bizz:course:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['bizz:course:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['bizz:course:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="courseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="课程名称" align="center" prop="courseName" show-overflow-tooltip/>
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上课地址" align="center" prop="location" show-overflow-tooltip width="180"/>
      <el-table-column label="报名上限" align="center" prop="maxQuota"/>
      <el-table-column label="已报人数" align="center" prop="currentQuota"/>
      <el-table-column label="报名截止" align="center" prop="enrollDeadline" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.enrollDeadline, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课程状态" align="center" prop="courseStatus">
        <template #default="scope">
          <dict-tag :options="course_status" :value="scope.row.courseStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="课程价格" align="center" prop="coursePrice" width="80">
        <template #default="scope">
          <span v-if="scope.row.coursePrice === 0">免费</span>
          <span v-else>{{ scope.row.coursePrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课程码" align="center" prop="infoQrUrl" width="120">
        <template #default="scope">
          <image-preview :src="scope.row.infoQrUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="签到码" align="center" prop="courseType">
        <template #default="scope">
          <image-preview :src="scope.row.checkinQrUrl" :width="50" :height="50" :download-file-name="scope.row.courseName + '课程签到码'"/>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createBy"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
              type="text"
              size="small"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              :disabled="scope.row.courseStatus == '0'"
              v-hasPermi="['bizz:course:edit']"
          >修改
          </el-button>
          <el-button
              type="text"
              size="small"
              icon="Delete"
              @click="handleDelete(scope.row)"
              :disabled="scope.row.courseStatus == '0'"
              v-hasPermi="['bizz:course:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改课程信息管理对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="courseRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程名称" prop="courseName">
          <el-input v-model="form.courseName" placeholder="请输入课程名称"/>
        </el-form-item>
        <el-form-item label="课程描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker clearable
                          v-model="form.startTime"
                          type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="请选择开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker clearable
                          v-model="form.endTime"
                          type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="请选择结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="上课地址" prop="location">
          <el-input v-model="form.location" placeholder="请输入上课地址"/>
        </el-form-item>
        <el-form-item label="最大报名人数" prop="maxQuota" >
          <el-input v-model="form.maxQuota" type="number" min="1" placeholder="请输入最大报名人数"/>
        </el-form-item>
        <el-form-item label="报名截止时间" prop="enrollDeadline">
          <el-date-picker clearable
                          v-model="form.enrollDeadline"
                          type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="请选择报名截止时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="课程价格" prop="coursePrice">
          <el-input v-model="form.coursePrice" placeholder="请输入课程价格"/>
        </el-form-item>
        <el-form-item label="允许角色">
          <el-select v-model="form.allowedRoleIds" multiple placeholder="请选择">
            <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName" :value="item.roleId" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {listCourse, getCourse, delCourse, addCourse, updateCourse} from "@/api/bizz/course";

const {proxy} = getCurrentInstance();
const {course_status} = proxy.useDict('course_status');

const courseList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const roleOptions = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    courseName: null,
    courseStatus: null,
  },
  rules: {
    courseName: [
      {required: true, message: "课程名称不能为空", trigger: "blur"}
    ],
    description: [
      {required: true, message: "课程描述不能为空", trigger: "blur"}
    ],
    startTime: [
      {required: true, message: "开始时间不能为空", trigger: "blur"}
    ],
    endTime: [
      {required: true, message: "结束时间不能为空", trigger: "blur"}
    ],
    location: [
      {required: true, message: "上课地址不能为空", trigger: "blur"}
    ],
    maxQuota: [
      {required: true, message: "最大报名人数不能为空", trigger: "blur"}
    ],
    currentQuota: [
      {required: true, message: "当前报名人数不能为空", trigger: "blur"}
    ],
    enrollDeadline: [
      {required: true, message: "报名截止时间不能为空", trigger: "blur"},
      {
        validator: (rule, value, callback) => {
          if (value && form.value.startTime && value > form.value.startTime) {
            callback(new Error("报名截止时间不能大于开始时间"));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    courseStatus: [
      {required: true, message: "课程状态不能为空", trigger: "change"}
    ],
    allowedRoleIds: [
      {required: true, message: "请选择允许的角色", trigger: "change"}
    ]
  }
});

const {queryParams, form, rules} = toRefs(data);

/** 查询课程信息管理列表 */
function getList() {
  loading.value = true;
  listCourse(queryParams.value).then(response => {
    courseList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    courseName: null,
    description: null,
    startTime: null,
    endTime: null,
    location: null,
    maxQuota: null,
    currentQuota: null,
    enrollDeadline: null,
    courseStatus: null,
    coursePrice: null,
    courseType: null,
    allowedRoles: '',
    allowedRoleIds: []
  };
  proxy.resetForm("courseRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getCourse().then(res => {
    roleOptions.value = res.roles || [];
    open.value = true;
  })
  title.value = "添加课程信息管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getCourse(_id).then(response => {
    form.value = response.data;
    roleOptions.value = response.roles || [];
    form.value.allowedRoleIds = response.allowedRoleIds || [];
    open.value = true;
    title.value = "修改课程信息管理";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["courseRef"].validate(valid => {
    if (valid) {
      // 处理 allowedRoleIds
      if (form.value.allowedRoleIds && form.value.allowedRoleIds.length > 0) {
        form.value.allowedRoles = form.value.allowedRoleIds.join(',');
      } else {
        form.value.allowedRoles = '';
      }
      if (form.value.id != null) {
        updateCourse(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCourse(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除课程信息管理编号为"' + _ids + '"的数据项？').then(function () {
    return delCourse(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bizz/course/export', {
    ...queryParams.value
  }, `course_${new Date().getTime()}.xlsx`)
}

getList();
</script>
