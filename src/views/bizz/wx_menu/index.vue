<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="菜单名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入菜单名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="菜单类型" prop="menuType">
        <el-select v-model="queryParams.menuType" placeholder="请选择菜单类型" clearable>
          <el-option label="个人设置" value="personal" />
          <el-option label="功能菜单" value="function" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['bizz:wx_menu:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bizz:wx_menu:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bizz:wx_menu:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['bizz:wx_menu:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="wxMenuList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="菜单ID" align="center" prop="id" width="80" />
      <el-table-column label="菜单名称" align="center" prop="title" />
      <el-table-column label="图标" align="center" prop="icon" width="100" show-overflow-tooltip />
      <el-table-column label="页面路径" align="center" prop="path" show-overflow-tooltip />
      <el-table-column label="菜单类型" align="center" prop="menuType" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.menuType === 'personal'" type="primary">个人设置</el-tag>
          <el-tag v-else-if="scope.row.menuType === 'function'" type="success">功能菜单</el-tag>
          <el-tag v-else type="info">{{ scope.row.menuType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="显示角色" align="center" prop="showFor" width="150" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.showFor">
            {{ scope.row.showFor.split(',').map(role => getRoleLabel(role.trim())).join(', ') }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" width="80" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['bizz:wx_menu:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['bizz:wx_menu:remove']">删除</el-button>
          <el-button link type="primary" icon="Sort" @click="handleSort(scope.row)" v-hasPermi="['bizz:wx_menu:edit']">排序</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改微信小程序菜单对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body draggable>
      <el-form ref="wxMenuRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="菜单名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="图标名称" prop="icon">
          <el-input v-model="form.icon" placeholder="请输入图标名称，如：setting-fill" />
        </el-form-item>
        <el-form-item label="页面路径" prop="path">
          <el-input v-model="form.path" placeholder="请输入页面路径，如：/pageA/profile" />
        </el-form-item>
        <el-form-item label="菜单类型" prop="menuType">
          <el-select v-model="form.menuType" placeholder="请选择菜单类型">
            <el-option label="个人设置" value="personal" />
            <el-option label="功能菜单" value="function" />
          </el-select>
        </el-form-item>
        <el-form-item label="显示角色" prop="showForArray">
          <el-select
            v-model="form.showForArray"
            multiple
            placeholder="请选择显示角色"
            style="width: 100%"
          >
            <el-option
                v-for="dict in sys_user_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="排序权重" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入菜单描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 排序对话框 -->
    <el-dialog title="菜单排序" v-model="sortOpen" width="400px" append-to-body draggable>
      <el-form ref="sortRef" :model="sortForm" label-width="100px">
        <el-form-item label="菜单名称">
          <span>{{ sortForm.title }}</span>
        </el-form-item>
        <el-form-item label="排序权重" prop="sortOrder">
          <el-input-number v-model="sortForm.sortOrder" :min="0" :max="999" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitSort">确 定</el-button>
          <el-button @click="cancelSort">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { listWxMenu, getWxMenu, delWxMenu, addWxMenu, updateWxMenu, updateMenuStatus, updateMenuSort } from "@/api/bizz/wx_menu";

const { proxy } = getCurrentInstance();
const { sys_user_type } = proxy.useDict('sys_user_type');

const wxMenuList = ref([]);
const open = ref(false);
const sortOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  sortForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    menuType: null,
    status: null,
  },
  rules: {
    title: [
      { required: true, message: "菜单名称不能为空", trigger: "blur" }
    ],
    icon: [
      { required: true, message: "图标名称不能为空", trigger: "blur" }
    ],
    path: [
      { required: true, message: "页面路径不能为空", trigger: "blur" }
    ],
    menuType: [
      { required: true, message: "菜单类型不能为空", trigger: "change" }
    ],
    showForArray: [
      {
        required: true,
        message: "显示角色不能为空",
        trigger: "change",
        validator: (rule, value, callback) => {
          if (!value || value.length === 0) {
            callback(new Error('请至少选择一个显示角色'));
          } else {
            callback();
          }
        }
      }
    ]
  }
});

const { queryParams, form, sortForm, rules } = toRefs(data);

/** 查询微信小程序菜单列表 */
function getList() {
  loading.value = true;
  listWxMenu(queryParams.value).then(response => {
    wxMenuList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    title: null,
    icon: null,
    path: null,
    menuType: null,
    showFor: null,
    showForArray: [],
    sortOrder: 0,
    status: 1,
    description: null
  };
  proxy.resetForm("wxMenuRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加微信小程序菜单";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getWxMenu(_id).then(response => {
    form.value = response.data;
    form.value.showForArray = form.value.showFor ? form.value.showFor.split(',') : [];
    open.value = true;
    title.value = "修改微信小程序菜单";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["wxMenuRef"].validate(valid => {
    if (valid) {
      // 处理显示角色数组
      form.value.showFor = form.value.showForArray.join(',');

      if (form.value.id != null) {
        updateWxMenu(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWxMenu(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除微信小程序菜单编号为"' + _ids + '"的数据项？').then(function() {
    return delWxMenu(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bizz/wx_menu/export', {
    ...queryParams.value
  }, `wx_menu_${new Date().getTime()}.xlsx`)
}

/** 状态修改 */
function handleStatusChange(row) {
  let text = row.status === 1 ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.title + '"菜单吗？').then(function() {
    return updateMenuStatus(row.id, row.status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function() {
    row.status = row.status === 0 ? 1 : 0;
  });
}

/** 排序按钮操作 */
function handleSort(row) {
  sortForm.value = {
    id: row.id,
    title: row.title,
    sortOrder: row.sortOrder
  };
  sortOpen.value = true;
}

/** 提交排序 */
function submitSort() {
  updateMenuSort(sortForm.value.id, sortForm.value.sortOrder).then(response => {
    proxy.$modal.msgSuccess("排序更新成功");
    sortOpen.value = false;
    getList();
  });
}

/** 取消排序 */
function cancelSort() {
  sortOpen.value = false;
}

/** 获取角色标签 */
function getRoleLabel(role) {
  const roleMap = {
    'all': '所有用户',
    'general': '普通用户',
    'aider': '急救员',
    'mentor': '急救导师',
    'disciple': '弟子',
    'founder': '创始人',
    'union_founder': '联合创始人',
  };
  return roleMap[role] || role;
}

getList();
</script>
