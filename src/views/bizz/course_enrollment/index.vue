<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="课程名称">
        <el-input v-model="queryParams.courseName" placeholder="请输入课程名称" clearable/>
      </el-form-item>
      <el-form-item label="用户昵称">
        <el-input v-model="queryParams.nickName" placeholder="请输入用户昵称" clearable/>
      </el-form-item>
      <el-form-item label="用户类型">
        <el-select v-model="queryParams.userType" placeholder="请选择用户类型" clearable>
          <el-option
              v-for="dict in sys_user_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报名时间">
        <el-date-picker
            v-model="queryParams.enrollTime"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            range-separator="至"
            clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['bizz:course_enrollment:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bizz:course_enrollment:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bizz:course_enrollment:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['bizz:course_enrollment:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="course_enrollmentList" @selection-change="handleSelectionChange">
      <el-table-column label="课程名称" align="center" prop="courseName"/>
      <el-table-column label="用户昵称" align="center" prop="nickName"/>
      <el-table-column label="用户类型" align="center" prop="userType">
        <template #default="scope">
          <template v-if="scope.row.userType">
            <dict-tag
                v-for="type in scope.row.userType.split(',')"
                :key="type.trim()"
                :options="sys_user_type"
                :value="type.trim()"
                style="margin-right: 4px; margin-bottom: 2px;"
            />
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="真实姓名" align="center" prop="realName"/>
      <el-table-column label="手机号码" align="center" prop="phoneNumber"/>
      <el-table-column label="报名时间" align="center" prop="enrollTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.enrollTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template #default="scope">-->
<!--          <el-button-->
<!--            type="text"-->
<!--            icon="Edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['bizz:course_enrollment:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            type="text"-->
<!--            icon="Delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['bizz:course_enrollment:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改课程报名记录对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="course_enrollmentRef" :model="form" :rules="rules" label-width="80px">
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { listCourse_enrollment, getCourse_enrollment, delCourse_enrollment, addCourse_enrollment, updateCourse_enrollment } from "@/api/bizz/course_enrollment";

const { proxy } = getCurrentInstance();
const { sys_yes_no,sys_user_type } = proxy.useDict('sys_yes_no', 'sys_user_type');
const course_enrollmentList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    enrollTime: null,
    createBy: null,
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询课程报名记录列表 */
function getList() {
  loading.value = true;
  let params = {
    ...queryParams.value
  };
  if (params.enrollTime && params.enrollTime.length === 2) {
    params.beginTime = params.enrollTime[0];
    params.endTime = params.enrollTime[1];
  }
  delete params.enrollTime;
  listCourse_enrollment(params).then(response => {
    course_enrollmentList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    userId: null,
    courseId: null,
    enrollStatus: null,
    enrollTime: null,
    cancelTime: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null
  };
  proxy.resetForm("course_enrollmentRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加课程报名记录";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getCourse_enrollment(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改课程报名记录";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["course_enrollmentRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateCourse_enrollment(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCourse_enrollment(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除课程报名记录编号为"' + _ids + '"的数据项？').then(function() {
    return delCourse_enrollment(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bizz/course_enrollment/export', {
    ...queryParams.value
  }, `course_enrollment_${new Date().getTime()}.xlsx`)
}

getList();
</script>
