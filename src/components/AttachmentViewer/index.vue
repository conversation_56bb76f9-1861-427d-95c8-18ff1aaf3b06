<template>
  <div>
    <!-- 表格中的附件按钮 -->
    <el-button
      v-if="mode === 'button'"
      type="info"
      text
      size="small"
      @click="handleViewAttachments"
    >
      查看附件
    </el-button>

    <!-- 表格中的图片/视频列显示 -->
    <div v-else-if="mode === 'inline'">
      <!-- 图片显示 -->
      <div v-if="type === 'image'" class="inline-container">
        <div v-if="imageAttachments.length > 0" class="image-container">
          <el-image
            v-for="(image, index) in imageAttachments.slice(0, 3)"
            :key="index"
            :src="getImageUrl(image)"
            :preview-src-list="imageAttachments.map(img => getImageUrl(img))"
            :initial-index="index"
            fit="cover"
            class="table-image"
            preview-teleported
          />
          <span v-if="imageAttachments.length > 3" class="more-count">
            +{{ imageAttachments.length - 3 }}
          </span>
        </div>
        <span v-else class="no-media">无图片</span>
      </div>

      <!-- 视频显示 -->
      <div v-else-if="type === 'video'" class="inline-container">
        <div v-if="videoAttachments.length > 0" class="video-container">
          <div
            v-for="(video, index) in videoAttachments.slice(0, 3)"
            :key="index"
            class="video-item"
            @click="playVideo(getVideoUrl(video))"
          >
            <el-icon class="video-icon"><VideoPlay /></el-icon>
          </div>
          <span v-if="videoAttachments.length > 3" class="more-count">
            +{{ videoAttachments.length - 3 }}
          </span>
        </div>
        <span v-else class="no-media">无视频</span>
      </div>
    </div>

    <!-- 附件预览对话框 -->
    <el-dialog
      title="附件预览"
      v-model="attachmentDialogVisible"
      width="800px"
      append-to-body
      draggable
    >
      <div class="attachment-preview">
        <!-- 图片预览 -->
        <div v-if="imageAttachments.length > 0" class="attachment-section">
          <h4>图片附件</h4>
          <div class="image-grid">
            <el-image
              v-for="(image, index) in imageAttachments"
              :key="index"
              :src="getImageUrl(image)"
              :preview-src-list="imageAttachments.map(img => getImageUrl(img))"
              :initial-index="index"
              fit="cover"
              class="preview-image"
              preview-teleported
            />
          </div>
        </div>
        
        <!-- 视频预览 -->
        <div v-if="videoAttachments.length > 0" class="attachment-section">
          <h4>视频附件</h4>
          <div class="video-grid">
            <div
              v-for="(video, index) in videoAttachments"
              :key="index"
              class="video-preview-item"
              @click="playVideo(getVideoUrl(video))"
            >
              <div class="video-thumbnail">
                <el-icon class="video-play-icon"><VideoPlay /></el-icon>
                <span class="video-name">视频 {{ index + 1 }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 无附件提示 -->
        <div v-if="imageAttachments.length === 0 && videoAttachments.length === 0" class="no-attachments">
          <el-empty description="暂无附件" />
        </div>
      </div>
    </el-dialog>

    <!-- 视频播放对话框 -->
    <el-dialog
      title="视频播放"
      v-model="videoDialogVisible"
      width="800px"
      append-to-body
      draggable
      @close="closeVideo"
    >
      <div class="video-player-container">
        <video
          ref="videoPlayer"
          :src="currentVideoUrl"
          controls
          width="100%"
          height="400"
          preload="metadata"
        >
          您的浏览器不支持视频播放
        </video>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { VideoPlay } from '@element-plus/icons-vue';

const props = defineProps({
  // 附件数据
  attachments: {
    type: Array,
    default: () => []
  },
  // 显示模式: button(按钮模式), inline(内联模式)
  mode: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'inline'].includes(value)
  },
  // 内联模式下的类型: image, video
  type: {
    type: String,
    default: 'image',
    validator: (value) => ['image', 'video'].includes(value)
  },
  // 数据结构配置
  config: {
    type: Object,
    default: () => ({
      urlField: 'attachmentUrl',  // URL字段名
      typeField: 'attachmentType', // 类型字段名
      imageType: 'image',         // 图片类型值
      videoType: 'video'          // 视频类型值
    })
  }
});

const attachmentDialogVisible = ref(false);
const videoDialogVisible = ref(false);
const currentVideoUrl = ref("");
const videoPlayer = ref(null);

// 计算属性：图片附件
const imageAttachments = computed(() => {
  if (!props.attachments || !Array.isArray(props.attachments)) return [];
  return props.attachments.filter(item => 
    item[props.config.typeField] === props.config.imageType
  );
});

// 计算属性：视频附件
const videoAttachments = computed(() => {
  if (!props.attachments || !Array.isArray(props.attachments)) return [];
  return props.attachments.filter(item => 
    item[props.config.typeField] === props.config.videoType
  );
});

// 获取图片URL
const getImageUrl = (attachment) => {
  return attachment[props.config.urlField] || '';
};

// 获取视频URL
const getVideoUrl = (attachment) => {
  return attachment[props.config.urlField] || '';
};

// 查看附件
const handleViewAttachments = () => {
  attachmentDialogVisible.value = true;
};

// 播放视频
const playVideo = (videoUrl) => {
  currentVideoUrl.value = videoUrl;
  videoDialogVisible.value = true;
};

// 关闭视频
const closeVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.pause();
    videoPlayer.value.currentTime = 0;
  }
  currentVideoUrl.value = "";
};
</script>

<style scoped>
.inline-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  align-items: center;
  position: relative;
}

.table-image {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
}

.video-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  align-items: center;
  position: relative;
}

.video-item {
  width: 30px;
  height: 30px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.video-item:hover {
  background: #ecf5ff;
  border-color: #409eff;
}

.video-icon {
  font-size: 16px;
  color: #409eff;
}

.more-count {
  font-size: 12px;
  color: #909399;
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: #fff;
  border-radius: 8px;
  padding: 0 4px;
  border: 1px solid #dcdfe6;
}

.no-media {
  color: #c0c4cc;
  font-size: 12px;
}

.attachment-preview {
  max-height: 500px;
  overflow-y: auto;
}

.attachment-section {
  margin-bottom: 20px;
}

.attachment-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
}

.preview-image {
  width: 120px;
  height: 120px;
  border-radius: 6px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
}

.video-preview-item {
  cursor: pointer;
}

.video-thumbnail {
  width: 150px;
  height: 100px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.video-thumbnail:hover {
  background: #ecf5ff;
  border-color: #409eff;
}

.video-play-icon {
  font-size: 24px;
  color: #409eff;
  margin-bottom: 5px;
}

.video-name {
  font-size: 12px;
  color: #606266;
}

.no-attachments {
  text-align: center;
  padding: 40px 0;
}

.video-player-container {
  text-align: center;
}
</style>
