<template>
  <div class="wx-menu-container">
    <!-- 个人设置菜单 -->
    <div v-if="menuType === 'personal'" class="personal-menu">
      <div class="menu-header">
        <h3>个人设置</h3>
      </div>
      <div class="menu-grid">
        <div 
          v-for="menu in filteredMenus" 
          :key="menu.id"
          class="menu-item"
          @click="handleMenuClick(menu)"
        >
          <div class="menu-icon">
            <el-icon><component :is="menu.icon" /></el-icon>
          </div>
          <div class="menu-title">{{ menu.title }}</div>
          <div class="menu-desc">{{ menu.description }}</div>
        </div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div v-else-if="menuType === 'function'" class="function-menu">
      <div class="menu-header">
        <h3>功能菜单</h3>
      </div>
      <div class="menu-grid">
        <div 
          v-for="menu in filteredMenus" 
          :key="menu.id"
          class="menu-item function-item"
          @click="handleMenuClick(menu)"
        >
          <div class="menu-icon">
            <el-icon><component :is="menu.icon" /></el-icon>
          </div>
          <div class="menu-title">{{ menu.title }}</div>
          <div class="menu-desc">{{ menu.description }}</div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading text="加载菜单中..." />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && filteredMenus.length === 0" class="empty-container">
      <el-empty description="暂无可用菜单" />
    </div>
  </div>
</template>

<script setup name="WxMenuList">
import { getPersonalMenus, getFunctionMenus } from "@/api/wx/menu";

const props = defineProps({
  // 菜单类型：personal | function
  menuType: {
    type: String,
    required: true,
    validator: (value) => ['personal', 'function'].includes(value)
  },
  // 是否显示描述
  showDescription: {
    type: Boolean,
    default: true
  },
  // 网格列数
  columns: {
    type: Number,
    default: 3
  }
});

const emit = defineEmits(['menu-click']);

const loading = ref(false);
const menuList = ref([]);
const filteredMenus = ref([]);

// 获取菜单数据
async function fetchMenus() {
  loading.value = true;
  try {
    let response;
    if (props.menuType === 'personal') {
      response = await getPersonalMenus();
    } else if (props.menuType === 'function') {
      response = await getFunctionMenus();
    }
    
    if (response && response.data) {
      menuList.value = response.data;
      filteredMenus.value = response.data;
    }
  } catch (error) {
    console.error('获取菜单失败:', error);
    ElMessage.error('获取菜单失败');
  } finally {
    loading.value = false;
  }
}

// 处理菜单点击
function handleMenuClick(menu) {
  try {
    // 触发父组件事件
    emit('menu-click', menu);

    // 如果有路径，进行页面跳转（这里可以根据实际需求调整）
    if (menu.path) {
      // 这里可以使用路由跳转或其他方式
      console.log('跳转到:', menu.path);
    }
  } catch (error) {
    console.error('菜单点击处理失败:', error);
  }
}

// 组件挂载时获取菜单
onMounted(() => {
  fetchMenus();
});

// 监听菜单类型变化
watch(() => props.menuType, () => {
  fetchMenus();
});

// 暴露刷新方法
defineExpose({
  refresh: fetchMenus
});
</script>

<style scoped>
.wx-menu-container {
  padding: 16px;
}

.menu-header {
  margin-bottom: 16px;
}

.menu-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(v-bind(columns), 1fr);
  gap: 16px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.menu-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.function-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.function-item:hover {
  box-shadow: 0 4px 20px 0 rgba(102, 126, 234, 0.4);
}

.menu-icon {
  font-size: 32px;
  margin-bottom: 12px;
  color: #409eff;
}

.function-item .menu-icon {
  color: white;
}

.menu-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.function-item .menu-title {
  color: white;
}

.menu-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.function-item .menu-desc {
  color: rgba(255, 255, 255, 0.8);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .menu-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .menu-item {
    padding: 16px 12px;
  }
  
  .menu-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  .menu-title {
    font-size: 14px;
  }
  
  .menu-desc {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .menu-grid {
    grid-template-columns: 1fr;
  }
}
</style>
