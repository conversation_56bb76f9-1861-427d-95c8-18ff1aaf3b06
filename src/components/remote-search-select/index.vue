<template>
  <div class="remote-search-select">
    <!-- 核心选择器组件 -->
    <el-select
        v-model="selectedValue"
        filterable
        remote
        reserve-keyword
        :remote-method="debouncedSearch"
        :loading="loading"
        :placeholder="placeholder"
        @change="handleChange"
        @focus="handleFocus"
        @clear="handleClear"
        clearable
        :disabled="disabled"
        :popper-class="'remote-select-dropdown'"
        :virtual-scroll="options.length > 100"
    >
    <!-- 选项列表 -->
    <el-option
        v-for="item in visibleOptions"
        :key="item[valueKey]"
        :label="item[labelKey]"
        :value="item[valueKey]"
    />

    <!-- 分页脚部 -->
    <template v-if="showFooter">
      <div class="select-footer">
        <div class="footer-content">
          <span class="total-count">共 {{ total }} 条</span>
          <el-pagination
              small
              :pager-count="isMobile ? 3 : 5"
              layout="prev, pager, next"
              :total="total"
              :page-size="pageSize"
              :current-page="currentPage"
              @current-change="handlePageChange"
              class="footer-pagination"
          />
        </div>
      </div>
    </template>
    </el-select>

    <!-- 智能加载状态 -->
    <div v-if="loading" class="loading-tip">
      <el-icon class="loading-icon"><loading /></el-icon>
      <span>系统在全力查找...</span>
    </div>
  </div>
</template>

<script setup>
import { useDebounceFn, useWindowSize } from '@vueuse/core'
import { Loading } from '@element-plus/icons-vue'

const { width } = useWindowSize()
const isMobile = computed(() => width.value < 768)

const props = defineProps({
  placeholder: { type: String, default: '请输入关键词搜索' },
  modelValue: { type: [String, Number], default: '' },
  remoteMethod: { type: Function, required: true },
  valueKey: { type: String, default: 'value' },
  labelKey: { type: String, default: 'label' },
  pageSize: { type: Number, default: 10 },
  debounceTime: { type: Number, default: 300 },
  getDetailMethod: { type: Function, default: null },
  disabled: { type: Boolean, default: false },
  cacheEnabled: { type: Boolean, default: true } // 新增缓存开关
})

const emit = defineEmits(['update:modelValue', 'change', 'select'])

//====== 响应式状态 ======//
const loading = ref(false)
const searchQuery = ref('')
const selectedValue = ref(null)
const currentPage = ref(1)
const total = ref(0)
const options = ref([])
const requestCache = new Map() // 缓存对象

//====== 计算属性 ======//
const showFooter = computed(() => total.value > props.pageSize)
const visibleOptions = computed(() =>
    // 虚拟滚动启用时仅渲染可见区域
    options.value.slice(0, props.virtualScroll ? 30 : options.value.length)
)

//====== 核心方法 ======//
const handleRemoteSearch = async (query) => {
  if (loading.value) return
  loading.value = true

  try {
    // 缓存策略
    const cacheKey = `${query}-${currentPage.value}`
    if (props.cacheEnabled && requestCache.has(cacheKey)) {
      const cached = requestCache.get(cacheKey)
      options.value = cached.list
      total.value = cached.total
      return
    }

    // 请求逻辑
    const data = await props.remoteMethod({
      keyWord: query,
      pageNum: currentPage.value,
      pageSize: props.pageSize
    })

    options.value = data.list || []
    total.value = data.total || 0

    // 缓存结果
    if (props.cacheEnabled) {
      requestCache.set(cacheKey, {
        list: [...options.value],
        total: total.value
      })
    }
  } catch (error) {
    console.error('[远程搜索异常]', error)
    options.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 防抖处理 (带取消功能)
const debouncedSearch = useDebounceFn(handleRemoteSearch, props.debounceTime)

//====== 交互处理器 ======//
const handleChange = (value) => {
  const selectedOption = options.value.find(item => item[props.valueKey] === value) || null
  selectedValue.value = value
  emit('update:modelValue', value)
  emit('change', value)
  if (selectedOption) emit('select', selectedOption)
}

const handleFocus = () => {
  if (!options.value.length && !loading.value) {
    debouncedSearch(searchQuery.value || '')
  }
}

const handleClear = () => {
  searchQuery.value = ''
  currentPage.value = 1
  selectedValue.value = null
  options.value = []
  total.value = 0
  emit('update:modelValue', '')
  emit('change', '')
  emit('select', null)
}

const handlePageChange = (page) => {
  currentPage.value = page
  debouncedSearch(searchQuery.value)
}

//====== 值初始化逻辑 ======//
const initializeDetail = async (value) => {
  if (!value || !props.getDetailMethod) return

  loading.value = true
  try {
    const detail = await props.getDetailMethod(value)
    if (detail) {
      options.value = [detail]
      selectedValue.value = detail[props.valueKey]
    }
  } catch (error) {
    console.error('[详情查询异常]', error)
  } finally {
    loading.value = false
  }
}

//====== 监听器 ======//
watch(searchQuery, (newQuery) => {
  currentPage.value = 1
  newQuery ? debouncedSearch(newQuery) : (options.value = [])
})

watch(
    () => props.modelValue,
    async (newValue) => {
      // 空值清除
      if (!newValue && newValue !== 0) {
        return handleClear()
      }

      // 检测是否需要获取详情
      const existing = options.value.some(item => item[props.valueKey] === newValue)
      if (!existing) await initializeDetail(newValue)
      else selectedValue.value = newValue
    },
    { immediate: true }
)

//====== 内存优化 ======//
onMounted(() => {
  // 滚动清理策略
  window.addEventListener('scroll', scrollCleanup)
})

onUnmounted(() => {
  // 卸载时清理
  requestCache.clear()
  window.removeEventListener('scroll', scrollCleanup)
  options.value = [] // 释放大数组内存
})

// 滚动时清理不可见选项
const scrollCleanup = useDebounceFn(() => {
  if (options.value.length > 100) {
    options.value = options.value.slice(0, 50)
  }
}, 1000)

//====== 暴露方法给父组件 ======//
defineExpose({
  handleClear
})
</script>

<style scoped lang="scss">
.remote-search-select {
  width: 100%;
  position: relative;

  /* 深度选择器优化 */
  :deep(.el-select) {
    width: 100%;

    &.is-disabled {
      opacity: 0.6;
    }
  }

  :deep(.remote-select-dropdown) {
    padding-bottom: 0;
    scrollbar-width: thin;
    scrollbar-color: var(--el-color-primary) var(--el-border-color-light);

    /* 下拉框滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
      &-thumb {
        border-radius: 3px;
        background: var(--el-color-primary-light-5);
        &:hover {
          background: var(--el-color-primary);
        }
      }
    }

    /* 下拉框内容容器 */
    .el-select-dropdown__wrap {
      max-height: calc(var(--el-select-dropdown-max-height) - 42px);
    }
  }
}

/* 页脚容器 */
.select-footer {
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
  position: sticky;
  bottom: 0;
  z-index: 10;

  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
  }

  .total-count {
    color: var(--el-text-color-secondary);
    font-size: 12px;
    white-space: nowrap;
  }
}

/* 加载状态 */
.loading-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  color: var(--el-color-primary);
  font-size: 13px;
  background: var(--el-color-primary-light-9);
  border-radius: 4px;
  margin-top: 4px;

  .loading-icon {
    margin-right: 6px;
    animation: rotating 2s linear infinite;
  }
}

@keyframes rotating {
  from { transform: rotate(0); }
  to { transform: rotate(360deg); }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 8px;
  }

  :deep(.footer-pagination) {
    transform: scale(0.85);
    transform-origin: right center;

    .btn-prev, .btn-next {
      padding: 0 5px;
    }
  }

  .loading-tip {
    font-size: 12px;
  }
}

/* 焦点强化 */
:focus-within .el-select {
  box-shadow: 0 0 0 2px var(--el-color-primary-light-5);
  border-radius: var(--el-border-radius-base);
}
</style>
