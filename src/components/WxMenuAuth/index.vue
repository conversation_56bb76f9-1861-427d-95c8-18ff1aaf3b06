<template>
  <div v-if="hasPermission">
    <slot></slot>
  </div>
</template>

<script setup name="WxMenuAuth">
import { checkMenuPermission, getUserRoles } from "@/api/wx/menu";

const props = defineProps({
  // 菜单ID
  menuId: {
    type: [String, Number],
    required: false
  },
  // 需要的角色列表
  roles: {
    type: Array,
    default: () => []
  },
  // 菜单路径（用于路径匹配）
  path: {
    type: String,
    required: false
  }
});

const hasPermission = ref(false);

// 检查权限
async function checkPermission() {
  try {
    // 如果指定了菜单ID，直接检查菜单权限
    if (props.menuId) {
      const response = await checkMenuPermission(props.menuId);
      hasPermission.value = response.data;
      return;
    }

    // 如果指定了角色列表，检查用户角色
    if (props.roles && props.roles.length > 0) {
      const userRolesResponse = await getUserRoles();
      const userRoles = userRolesResponse.data.roles || [];
      
      // 检查用户是否拥有所需角色之一
      hasPermission.value = props.roles.some(role => 
        userRoles.includes(role) || role === 'all'
      );
      return;
    }

    // 默认有权限
    hasPermission.value = true;
  } catch (error) {
    console.error('权限检查失败:', error);
    hasPermission.value = false;
  }
}

// 组件挂载时检查权限
onMounted(() => {
  checkPermission();
});

// 监听props变化，重新检查权限
watch([() => props.menuId, () => props.roles, () => props.path], () => {
  checkPermission();
});
</script>

<style scoped>
/* 权限验证组件样式 */
</style>
