import { login, logout, getInfo, refresh } from '@/api/login'
import { getToken, setToken, removeToken, getRefreshToken, setRefreshToken, removeRefreshToken } from '@/utils/auth'
import { isHttp, isEmpty } from "@/utils/validate"
import defAva from '@/assets/images/profile.jpg'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      refreshToken: getRefreshToken(),
      id: '',
      name: '',
      avatar: '',
      roles: [],
      permissions: []
    }),
    actions: {
      // 登录
      login(userInfo) {
        const username = userInfo.username.trim()
        const password = userInfo.password
        const code = userInfo.code
        const uuid = userInfo.uuid
        return new Promise((resolve, reject) => {
          login(username, password, code, uuid).then(res => {
            // 处理双token响应
            const accessToken = res.data?.accessToken || res.token
            const refreshToken = res.data?.refreshToken

            setToken(accessToken)
            this.token = accessToken

            if (refreshToken) {
              setRefreshToken(refreshToken)
              this.refreshToken = refreshToken
            }

            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取用户信息
      getInfo() {
        return new Promise((resolve, reject) => {
          getInfo().then(res => {
            const user = res.user
            let avatar = user.avatar || ""
            if (!isHttp(avatar)) {
              avatar = (isEmpty(avatar)) ? defAva : import.meta.env.VITE_APP_BASE_API + avatar
            }
            if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
              this.roles = res.roles
              this.permissions = res.permissions
            } else {
              this.roles = ['ROLE_DEFAULT']
            }
            this.id = user.userId
            this.name = user.userName
            this.avatar = avatar
            resolve(res)
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 刷新token
      refreshAccessToken() {
        return new Promise((resolve, reject) => {
          const currentRefreshToken = this.refreshToken
          if (!currentRefreshToken) {
            reject(new Error('No refresh token available'))
            return
          }

          refresh(currentRefreshToken).then(res => {
            const accessToken = res.data?.accessToken || res.accessToken
            const newRefreshToken = res.data?.refreshToken || res.refreshToken

            if (accessToken) {
              setToken(accessToken)
              this.token = accessToken
            }

            if (newRefreshToken) {
              setRefreshToken(newRefreshToken)
              this.refreshToken = newRefreshToken
            }

            resolve(accessToken)
          }).catch(error => {
            // 刷新失败，清除所有token
            this.token = ''
            this.refreshToken = ''
            removeToken()
            removeRefreshToken()
            reject(error)
          })
        })
      },
      // 退出系统
      logOut() {
        return new Promise((resolve, reject) => {
          logout(this.token).then(() => {
            this.token = ''
            this.refreshToken = ''
            this.roles = []
            this.permissions = []
            removeToken()
            removeRefreshToken()
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      }
    }
  })

export default useUserStore
