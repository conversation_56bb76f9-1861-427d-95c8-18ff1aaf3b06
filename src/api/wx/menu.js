import request from '@/utils/request'

// 获取个人设置菜单
export function getPersonalMenus() {
  return request({
    url: '/wx/menu/personal',
    method: 'get'
  })
}

// 获取功能菜单
export function getFunctionMenus() {
  return request({
    url: '/wx/menu/function',
    method: 'get'
  })
}

// 检查菜单访问权限
export function checkMenuPermission(menuId) {
  return request({
    url: `/api/menu/check/${menuId}`,
    method: 'get'
  })
}

// 获取当前用户角色信息
export function getUserRoles() {
  return request({
    url: '/api/user/roles',
    method: 'get'
  })
}

// 获取用户常用菜单
export function getFrequentMenus(limit = 5) {
  return request({
    url: '/wx/menu/frequent',
    method: 'get',
    params: { limit }
  })
}

// 获取所有可用菜单（用于权限验证）
export function getAllAvailableMenus() {
  return request({
    url: '/wx/menu/available',
    method: 'get'
  })
}
