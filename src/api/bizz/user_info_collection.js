import request from '@/utils/request.js'

// 查询用户信息收集列表
export function listCollection(query) {
    return request({
        url: '/bizz/user_info_collection/list',
        method: 'get',
        params: query
    })
}

// 查询用户信息收集详细
export function getCollection(id) {
    return request({
        url: '/bizz/user_info_collection/' + id,
        method: 'get'
    })
}

// 新增用户信息收集
export function addCollection(data) {
    return request({
        url: '/bizz/user_info_collection',
        method: 'post',
        data: data
    })
}

// 修改用户信息收集
export function updateCollection(data) {
    return request({
        url: '/bizz/user_info_collection',
        method: 'put',
        data: data
    })
}

// 删除用户信息收集
export function delCollection(id) {
    return request({
        url: '/bizz/user_info_collection/' + id,
        method: 'delete'
    })
}
