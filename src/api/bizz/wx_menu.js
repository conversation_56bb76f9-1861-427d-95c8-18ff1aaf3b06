import request from '@/utils/request'

// 查询微信小程序菜单列表
export function listWxMenu(query) {
  return request({
    url: '/bizz/wx_menu/list',
    method: 'get',
    params: query
  })
}

// 查询微信小程序菜单详细
export function getWxMenu(id) {
  return request({
    url: '/bizz/wx_menu/' + id,
    method: 'get'
  })
}

// 新增微信小程序菜单
export function addWxMenu(data) {
  return request({
    url: '/bizz/wx_menu',
    method: 'post',
    data: data
  })
}

// 修改微信小程序菜单
export function updateWxMenu(data) {
  return request({
    url: '/bizz/wx_menu',
    method: 'put',
    data: data
  })
}

// 删除微信小程序菜单
export function delWxMenu(id) {
  return request({
    url: '/bizz/wx_menu/' + id,
    method: 'delete'
  })
}


// 更新菜单状态
export function updateMenuStatus(id, status) {
  return request({
    url: `/bizz/wx_menu/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 更新菜单排序
export function updateMenuSort(id, sortOrder) {
  return request({
    url: `/bizz/wx_menu/${id}/sort`,
    method: 'put',
    data: { sortOrder }
  })
}
