import request from '@/utils/request'

// 查询证件申请列表
export function listApply(query) {
  return request({
    url: '/bizz/apply/list',
    method: 'get',
    params: query
  })
}

// 查询证件申请详细
export function getApply(id) {
  return request({
    url: '/bizz/apply/' + id,
    method: 'get'
  })
}

// 新增证件申请
export function addApply(data) {
  return request({
    url: '/bizz/apply',
    method: 'post',
    data: data
  })
}

// 修改证件申请
export function updateApply(data) {
  return request({
    url: '/bizz/apply',
    method: 'put',
    data: data
  })
}

// 删除证件申请
export function delApply(id) {
  return request({
    url: '/bizz/apply/' + id,
    method: 'delete'
  })
}

// 审批证件申请
export function auditApply(id,type) {
  return request({
    url: `/bizz/apply/audit/${id}`,
    method: 'get',
    params: {type}
  })
}