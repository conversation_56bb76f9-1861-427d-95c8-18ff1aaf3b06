import request from '@/utils/request.js'


// 查询用户列表
export function listMemberInfo(query) {
  return request({
    url: '/bizz/member_info/list',
    method: 'get',
    params: query
  })
}

// 更改用户类型
export function updateMemberType(data) {
  return request({
    url: '/bizz/member_info/updateMemberType',
    method: 'put',
    data: data
  })
}

export function selectMemberList(query) {
  return request({
    url: '/bizz/member_info/selectMemberList',
    method: 'get',
    params: query
  })
}

// 变更邀请人
export function updateMemberInviter(ids, newInviterId) {
  return request({
    url: `/bizz/member_info/updateMemberInviter/${ids}/${newInviterId}`,
    method: 'put'
  })
}