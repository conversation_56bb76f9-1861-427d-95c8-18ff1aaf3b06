import request from '@/utils/request'

// 查询贡献值配置列表
export function listContribution_action_config(query) {
  return request({
    url: '/bizz/contribution_action_config/list',
    method: 'get',
    params: query
  })
}

// 查询贡献值配置详细
export function getContribution_action_config(id) {
  return request({
    url: '/bizz/contribution_action_config/' + id,
    method: 'get'
  })
}

// 新增贡献值配置
export function addContribution_action_config(data) {
  return request({
    url: '/bizz/contribution_action_config',
    method: 'post',
    data: data
  })
}

// 修改贡献值配置
export function updateContribution_action_config(data) {
  return request({
    url: '/bizz/contribution_action_config',
    method: 'put',
    data: data
  })
}

// 删除贡献值配置
export function delContribution_action_config(id) {
  return request({
    url: '/bizz/contribution_action_config/' + id,
    method: 'delete'
  })
}
