import request from '@/utils/request'

// 查询活动审核列表
export function listActivity_review(query) {
  return request({
    url: '/bizz/activity_review/list',
    method: 'get',
    params: query
  })
}

// 查询活动审核详细
export function getActivity_review(id) {
  return request({
    url: '/bizz/activity_review/' + id,
    method: 'get'
  })
}

// 新增活动审核
export function addActivity_review(data) {
  return request({
    url: '/bizz/activity_review',
    method: 'post',
    data: data
  })
}

// 修改活动审核
export function updateActivity_review(data) {
  return request({
    url: '/bizz/activity_review',
    method: 'put',
    data: data
  })
}

// 删除活动审核
export function delActivity_review(id) {
  return request({
    url: '/bizz/activity_review/' + id,
    method: 'delete'
  })
}

// 批量审批活动审核
export function batchAuditActivityReview(ids, status) {
  return request({
    url: `/bizz/activity_review/batchAudit/${ids}`,
    method: 'get',
    params: { status }
  })
}
