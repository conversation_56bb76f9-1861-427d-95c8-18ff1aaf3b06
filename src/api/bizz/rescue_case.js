import request from '@/utils/request'

// 查询急救案例列表
export function listRescue_case(query) {
  return request({
    url: '/bizz/rescue_case/list',
    method: 'get',
    params: query
  })
}

// 查询急救案例详细
export function getRescue_case(id) {
  return request({
    url: '/bizz/rescue_case/' + id,
    method: 'get'
  })
}

// 新增急救案例
export function addRescue_case(data) {
  return request({
    url: '/bizz/rescue_case',
    method: 'post',
    data: data
  })
}

// 修改急救案例
export function updateRescue_case(data) {
  return request({
    url: '/bizz/rescue_case',
    method: 'put',
    data: data
  })
}

// 删除急救案例
export function delRescue_case(id) {
  return request({
    url: '/bizz/rescue_case/' + id,
    method: 'delete'
  })
}
// 批量审批急救案例
export function batchAuditRescueCase(ids, status) {
  return request({
    url: `/bizz/rescue_case/batchAudit/${ids}`,
    method: 'get',
    params: { status }
  })
}
