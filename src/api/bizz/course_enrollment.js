import request from '@/utils/request'

// 查询课程报名记录列表
export function listCourse_enrollment(query) {
  return request({
    url: '/bizz/course_enrollment/list',
    method: 'get',
    params: query
  })
}

// 查询课程报名记录详细
export function getCourse_enrollment(id) {
  return request({
    url: '/bizz/course_enrollment/' + id,
    method: 'get'
  })
}

// 新增课程报名记录
export function addCourse_enrollment(data) {
  return request({
    url: '/bizz/course_enrollment',
    method: 'post',
    data: data
  })
}

// 修改课程报名记录
export function updateCourse_enrollment(data) {
  return request({
    url: '/bizz/course_enrollment',
    method: 'put',
    data: data
  })
}

// 删除课程报名记录
export function delCourse_enrollment(id) {
  return request({
    url: '/bizz/course_enrollment/' + id,
    method: 'delete'
  })
}
