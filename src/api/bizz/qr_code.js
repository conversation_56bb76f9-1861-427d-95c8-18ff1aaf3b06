import request from '@/utils/request'

// 查询二维码信息列表
export function listQr_code(query) {
  return request({
    url: '/bizz/qr_code/list',
    method: 'get',
    params: query
  })
}

// 查询二维码信息详细
export function getQr_code(id) {
  return request({
    url: '/bizz/qr_code/' + id,
    method: 'get'
  })
}

// 新增二维码信息
export function addQr_code(data) {
  return request({
    url: '/bizz/qr_code',
    method: 'post',
    data: data
  })
}

// 修改二维码信息
export function updateQr_code(data) {
  return request({
    url: '/bizz/qr_code',
    method: 'put',
    data: data
  })
}

// 删除二维码信息
export function delQr_code(id) {
  return request({
    url: '/bizz/qr_code/' + id,
    method: 'delete'
  })
}
