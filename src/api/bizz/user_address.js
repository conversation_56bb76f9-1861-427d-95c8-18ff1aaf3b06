import request from '@/utils/request'

// 查询用户地址管理列表
export function listUser_address(query) {
  return request({
    url: '/bizz/user_address/list',
    method: 'get',
    params: query
  })
}

// 查询用户地址管理详细
export function getUser_address(id) {
  return request({
    url: '/bizz/user_address/' + id,
    method: 'get'
  })
}

// 新增用户地址管理
export function addUser_address(data) {
  return request({
    url: '/bizz/user_address',
    method: 'post',
    data: data
  })
}

// 修改用户地址管理
export function updateUser_address(data) {
  return request({
    url: '/bizz/user_address',
    method: 'put',
    data: data
  })
}

// 删除用户地址管理
export function delUser_address(id) {
  return request({
    url: '/bizz/user_address/' + id,
    method: 'delete'
  })
}
