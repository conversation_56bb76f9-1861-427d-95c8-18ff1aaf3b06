import request from '@/utils/request'

// 查询用户身份申请列表
export function listUser_apply(query) {
  return request({
    url: '/bizz/user_apply/list',
    method: 'get',
    params: query
  })
}

// 查询用户身份申请详细
export function getUser_apply(id) {
  return request({
    url: '/bizz/user_apply/' + id,
    method: 'get'
  })
}

// 新增用户身份申请
export function addUser_apply(data) {
  return request({
    url: '/bizz/user_apply',
    method: 'post',
    data: data
  })
}

// 修改用户身份申请
export function updateUser_apply(data) {
  return request({
    url: '/bizz/user_apply',
    method: 'put',
    data: data
  })
}

// 删除用户身份申请
export function delUser_apply(id) {
  return request({
    url: '/bizz/user_apply/' + id,
    method: 'delete'
  })
}

// 审批用户身份申请
export function auditUser_apply(id, type) {
  return request({
    url: `/bizz/user_apply/audit/${id}`,
    method: 'get',
    params: {type}
  })
}
