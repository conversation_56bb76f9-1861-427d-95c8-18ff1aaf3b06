import request from '@/utils/request'

// 查询贡献值日志列表
export function listContribution_log(query) {
  return request({
    url: '/bizz/contribution_log/list',
    method: 'get',
    params: query
  })
}

// 查询贡献值日志详细
export function getContribution_log(id) {
  return request({
    url: '/bizz/contribution_log/' + id,
    method: 'get'
  })
}

// 新增贡献值日志
export function addContribution_log(data) {
  return request({
    url: '/bizz/contribution_log',
    method: 'post',
    data: data
  })
}

// 修改贡献值日志
export function updateContribution_log(data) {
  return request({
    url: '/bizz/contribution_log',
    method: 'put',
    data: data
  })
}

// 删除贡献值日志
export function delContribution_log(id) {
  return request({
    url: '/bizz/contribution_log/' + id,
    method: 'delete'
  })
}
