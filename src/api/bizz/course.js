import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询课程信息管理列表
export function listCourse(query) {
  return request({
    url: '/bizz/course/list',
    method: 'get',
    params: query
  })
}

// 查询课程信息管理详细
export function getCourse(id) {
  return request({
    url: '/bizz/course/' + parseStrEmpty(id),
    method: 'get'
  })
}

// 新增课程信息管理
export function addCourse(data) {
  return request({
    url: '/bizz/course',
    method: 'post',
    data: data
  })
}

// 修改课程信息管理
export function updateCourse(data) {
  return request({
    url: '/bizz/course',
    method: 'put',
    data: data
  })
}

// 删除课程信息管理
export function delCourse(id) {
  return request({
    url: '/bizz/course/' + id,
    method: 'delete'
  })
}
