import request from '@/utils/request'

// 查询课程签到记录列表
export function listCourse_attendance(query) {
  return request({
    url: '/bizz/course_attendance/list',
    method: 'get',
    params: query
  })
}

// 查询课程签到记录详细
export function getCourse_attendance(id) {
  return request({
    url: '/bizz/course_attendance/' + id,
    method: 'get'
  })
}

// 新增课程签到记录
export function addCourse_attendance(data) {
  return request({
    url: '/bizz/course_attendance',
    method: 'post',
    data: data
  })
}

// 修改课程签到记录
export function updateCourse_attendance(data) {
  return request({
    url: '/bizz/course_attendance',
    method: 'put',
    data: data
  })
}

// 删除课程签到记录
export function delCourse_attendance(id) {
  return request({
    url: '/bizz/course_attendance/' + id,
    method: 'delete'
  })
}
